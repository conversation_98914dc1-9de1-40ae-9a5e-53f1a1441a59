

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/division1/hive/solar_chart_repository.dart';
import 'package:si/division1/model/solar_chart_model.dart';
import 'package:si/kawosoti/schema/site_one/provider/site_one_provider.dart';

import '../../../constants/state_model.dart';



final solarchartRepoProvider = Provider<ISolarChartRepository>((ref) {
  return SolarChartRepository();
});


final solarChartProvider = StateNotifierProvider.family
    .autoDispose<SolarchartController, Resource<SolarChartModel>, String>((ref, req) {
  final repository = ref.read(solarchartRepoProvider);
  return SolarchartController(
      apiRepository: repository, date: DateTime.now(), siteId: req);
});

class SolarchartController extends StateNotifier<Resource<SolarChartModel>> {
  SolarchartController(
      {required this.apiRepository, required this.date, required this.siteId})
      : super(const Resource.unInitialized()) {
    getData(DateTime.now());
  }

  final ISolarChartRepository apiRepository;
  final DateTime? date;
  final String siteId;

  Future<void> getData(DateTime dateTime) async {
    state = const Resource.loading();
    try {
      final response =
      await apiRepository.getSolarChartData(siteId: siteId, date: dateTime);
      final ampList = <FlSpot>[];
      final voltageList = <FlSpot>[];
      final freqList = <FlSpot>[];
      final powerList = <FlSpot>[];
      final dischargeList = <FlSpot>[];

      for (int i = 0; i < response.length; i++) {
        ampList.add(FlSpot(getMins(response[i]!.time!), double.parse(response[i]!.amps!.toStringAsFixed(2))));
        voltageList.add(FlSpot(getMins(response[i]!.time!), double.parse(response[i]!.volt!.toStringAsFixed(2))));
        freqList.add(FlSpot(getMins(response[i]!.time!), double.parse(response[i]!.freq!.toStringAsFixed(2))));
        powerList.add(FlSpot(getMins(response[i]!.time!), double.parse((response[i]!.amps!*response[i].volt!).toStringAsFixed(2))));
        dischargeList.add(FlSpot(getMins(response[i]!.time!), double.parse(response[i]!.amps!.toStringAsFixed(2))));
      }
      state = Resource.success(
          SolarChartModel(
              ampList: ampList,
              voltageList: voltageList,
              freqList: freqList,
              powerList: powerList,
              dischargeList: dischargeList,
              minX: 0,
              minY: 0,
              maxX: 1440,
              maxY: 9),
          "");
    } catch (error) {
      state = Resource.error(error.toString());
    }
  }

  double getMins(DateTime date) {
    //print(date);
    return double.parse((date.hour * 60 + date.minute).toString());
  }
}
