import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/division1/homepage/solarpump/solar_chart_local_repository.dart';
import 'package:si/division1/homepage/solarpump/solar_chart_repository.dart';
import 'package:si/division1/model/solar_chart_model.dart';

import '../../../constants/state_model.dart';

final solarchartRepoProvider = Provider<ISolarChartRepository>((ref) {
  return SolarChartRepository();
});

final hiveRepoProvider = Provider<ISolarChartHiveRepository>((ref) {
  return SolarChartHiveRepository();
});

final solarChartProvider = StateNotifierProvider.family
    .autoDispose<SolarchartController, Resource<SolarChartModel>, String>(
        (ref, req) {
  final repository = ref.read(solarchartRepoProvider);
  final hiveRepo = ref.read(hiveRepoProvider);
  return SolarchartController(
      apiRepository: repository, hiveRepository: hiveRepo, date: DateTime.now(), siteId: req);
});

class SolarchartController extends StateNotifier<Resource<SolarChartModel>> {
  SolarchartController(
      {required this.apiRepository, required this.hiveRepository,required this.date, required this.siteId})
      : super(const Resource.unInitialized()) {
    getData(DateTime.now());
  }

  final ISolarChartRepository apiRepository;
  final ISolarChartHiveRepository hiveRepository;
  final DateTime? date;
  final String siteId;

  Future<void> getData(DateTime dateTime) async {
    state = const Resource.loading();
    try {
      final ampList = <FlSpot>[];
      final voltageList = <FlSpot>[];
      final freqList = <FlSpot>[];
      final powerList = <FlSpot>[];
      final dischargeList = <FlSpot>[];
      if(dateTime.year==DateTime.now().year && dateTime.month==DateTime.now().month && dateTime.day==DateTime.now().day){
        final lastData = hiveRepository.isDataAvailable(dateTime);
        if(lastData!=null){ /*If today's data is already available*/
          if(DateTime.now().hour - lastData.hour >1){
            final response = await apiRepository.getSolarChartData(siteId: "motorHistory",date: dateTime);
            await hiveRepository.insertSiteHistoryifNotExists(response);
          }
        }else{
          final response = await apiRepository.getSolarChartData(siteId: "motorHistory",date: dateTime);
          await hiveRepository.insertSiteHistory(response);
        }
      }else{  /*If Not today*/
        final lastData = hiveRepository.isDataAvailable(dateTime);
        if(lastData==null){
          final response = await apiRepository.getSolarChartData(siteId: "motorHistory",date: dateTime);
          await hiveRepository.insertSiteHistory(response);
        }
      }
      final list = await hiveRepository.getSiteHistoryHiveModel(dateTime);

      final response =
          await apiRepository.getSolarChartData(siteId: siteId, date: dateTime);


      for (int i = 0; i < response.length; i++) {
        final dataPoint = response[i]!;
        final timeInMins = getMinsFromTimestamp(dataPoint.time!);

        ampList.add(FlSpot(
            timeInMins, double.parse(dataPoint.amps!.toStringAsFixed(2))));
        voltageList.add(FlSpot(
            timeInMins, double.parse(dataPoint.volt!.toStringAsFixed(2))));
        freqList.add(FlSpot(
            timeInMins, double.parse(dataPoint.freq!.toStringAsFixed(2))));
        powerList.add(FlSpot(
            timeInMins,
            double.parse(
                (dataPoint.amps! * dataPoint.volt!).toStringAsFixed(2))));
        dischargeList.add(FlSpot(
            timeInMins, double.parse(dataPoint.amps!.toStringAsFixed(2))));


      }
      state = Resource.success(
          SolarChartModel(
              ampList: ampList,
              voltageList: voltageList,
              freqList: freqList,
              powerList: powerList,
              dischargeList: dischargeList,
              minX: 0,
              minY: 0,
              maxX: 1440,
              maxY: 9),
          "");
    } catch (error) {
      state = Resource.error(error.toString());
    }
  }

  double getMinsFromTimestamp(int timestamp) {
    // Convert timestamp (milliseconds since epoch) to DateTime
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // Return minutes from start of day (0-1439)
    return double.parse((date.hour * 60 + date.minute).toString());
  }
}
