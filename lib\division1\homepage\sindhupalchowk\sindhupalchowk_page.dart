

import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/division1/homepage/widgets/distribution_widget.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/homepage/widgets/meter/sensor_widget.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:text_scroll/text_scroll.dart';
import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../provider/auth_provider.dart';
import '../../provider/division_provider.dart';
import '../widgets/meter/sensor_based_valve.dart';
import '../widgets/tank_widget.dart';
import '../widgets/valve_widget.dart';

class SindhulpalchowkPage extends HookConsumerWidget{
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var size = MediaQuery.of(context).size;
    final  appDimension = ref.read(sindhulpalchowkProvider);
    final prefs = ref.read(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));

    return BaseScaffold(
      showAppBar: false,
        child: Center(
          child: Container(
            height: size.height,
            width: size.width,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF87CEFA), // Light Blue (Sky color)
                  Color(0xFFE0FFFF),
                  Color(0xFF87CEFA), // Light Blue (Sky color)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: siteSetting.when(success: (data, message){
               return  Column(
                 children: [
                   gapH8,
                   Container(
                     width: double.infinity,
                     child: Center(
                       child: TextScroll(
                         'जुगल थालखर्क होल्छे खानेपानी मुल उपभोक्ता तथा सरसफाइ समिती परिवार, चौतारा सिन्धुपाल्चोक                        ',
                         mode: TextScrollMode.bouncing,
                         velocity: Velocity(pixelsPerSecond: Offset(150, 0)),
                         delayBefore: Duration(milliseconds: 500),
                         pauseBetween: Duration(milliseconds: 50),
                         textAlign: TextAlign.right,
                         style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                         ),
                       ),
                     ),
                   ),
                   Expanded(
                     child: Stack(
                       children: [

                         Positioned(
                             top: appDimension.valveControllerY(x: size.width, y: size.height),
                             left: appDimension.valveControllerX(x: size.width, y: size.height),
                             child: SensorBasedValve(size: size, appDimension: appDimension)),
                         Positioned(
                           left: 0,
                           top: appDimension.wellY(x: size.width , y: size.height),
                           child: Container(
                             height: appDimension.wellHeight(x :size.width, y:size.height),
                             width: 110,
                             child: Column(
                               children: [
                                 Expanded(
                                   child: RotatedBox(
                                     quarterTurns: 2,
                                     child: LoadingAnimationWidget.prograssiveDots(
                                       color: Colors.lightBlueAccent,
                                       size: 40,
                                     ),
                                   ),
                                 ),
                                 Expanded(
                                   child: RotatedBox(
                                     quarterTurns: 2,
                                     child: LoadingAnimationWidget.prograssiveDots(
                                       color: Colors.lightBlueAccent,
                                       size: 40,
                                     ),
                                   ),
                                 ),
                                 Expanded(
                                   child: RotatedBox(
                                     quarterTurns: 2,
                                     child: LoadingAnimationWidget.prograssiveDots(
                                       color: Colors.lightBlueAccent,
                                       size: 40,
                                     ),
                                   ),
                                 ),
                                 Expanded(
                                   child: RotatedBox(
                                     quarterTurns: 2,
                                     child: LoadingAnimationWidget.prograssiveDots(
                                       color: Colors.lightBlueAccent,
                                       size: 40,
                                     ),
                                   ),
                                 ),
                               ],
                             ),
                           ),
                         ),
                         Positioned(
                             top: appDimension.wellY(x :size.width, y:size.height),
                             left: appDimension.wellX(x :size.width, y:size.height),
                             child: Stack(
                               children: [
                                 Image.asset("assets/icons/dam.png",
                                   fit: BoxFit.fill,
                                   height: appDimension.wellHeight(x :size.width, y:size.height),
                                   width: appDimension.wellWidth(x :size.width, y:size.height),),
                                 Positioned(
                                   left: 80,
                                     top: 32,
                                     child: Text("मुहानको पानी", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),))
                               ],
                             )),
                         Positioned(
                           top: appDimension.l6Y(x: size.width, y: size.height),
                           left: appDimension.l6X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l6Length(x: size.width, y: size.height),
                             name: "L6",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             singleFlow: true,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l7Y(x: size.width, y: size.height),
                           left: appDimension.l7X(x: size.width, y: size.height),
                           child: VerticalPipeline(
                             length: appDimension.l7Length(x: size.width, y: size.height),
                             name: "L7",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: true,
                             topFlow: false,
                             singleFlow: true,
                           ),
                         ),
                         Positioned(
                           key: const ValueKey('T1'),
                           left: appDimension.t1X(x: size.width, y: size.height),
                           top: appDimension.t1Y(x: size.width, y: size.height),
                           child:  TankBlock(
                             tankId: "10",
                             tankSettingModel: data!.tank10!,
                             height: appDimension.t1Height(
                                 x: size.width, y: size.height),
                             width: appDimension.t1Width(
                                 x: size.width, y: size.height),
                           ),
                         ),
                         Positioned(
                           key: const ValueKey('T2'),
                           left: appDimension.t2X(x: size.width, y: size.height),
                           top: appDimension.t2Y(x: size.width, y: size.height),
                           child:  TankBlock(
                             tankId: "11",
                             tankSettingModel: data!.tank11!,
                             height: appDimension.t2Height(
                                 x: size.width, y: size.height),
                             width: appDimension.t2Width(
                                 x: size.width, y: size.height),
                           ),
                         ),
                         Positioned(
                           top: appDimension.sedimentY(x: size.width, y: size.height),
                           left: appDimension.sedimentX(x: size.width, y: size.height),
                           child: Column(
                             children: [
                               Padding(
                                 padding: const EdgeInsets.all(8.0),
                                 child: Text("Sediment Tank"),
                               ),
                               Image.asset(
                                 "assets/icons/sediment.png",
                                 fit: BoxFit.fill,
                                 height: appDimension.sedimentHeight(x: size.width, y: size.height),
                                 width: appDimension.sedimentWidth(x: size.width, y: size.height),
                               ),
                             ],
                           ),
                         ),
                         Positioned(
                           top: 16,
                             right: 24,
                             child: Row(
                               children: [
                                 Image.asset("assets/icons/emblem.webp", height:100,width:100,),
                                 Text("संघीय खानेपानी तथा ढल \nव्यवस्थापन आयोजना, भक्तपुर",
                                 textAlign: TextAlign.center,
                                 style: TextStyle(fontSize: 18,fontWeight: FontWeight.bold),),
                               ],
                             )),

                         Positioned(
                           top: 16,
                           left: 16,
                           child: Column(
                             children: [
                               Text("Powered by"),
                               gapH8,

                               Image.asset("assets/icons/logo.webp",height: 80, width: 80),
                               gapH8,
                               Text("Swodeshi Innovation", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),),

                             ],
                           )
                         ),

                         Positioned(
                           right: 16,
                           bottom: 16,
                           child: IconButton(
                             icon: const Icon(Icons.logout_outlined),
                             onPressed: () async {
                               final bool didRequestSignOut =
                                   await showAlertDialog(
                                     context: context,
                                     title: 'Logout',
                                     content:
                                     'Are you sure you want to logout?',
                                     cancelActionText: 'Cancel',
                                     defaultActionText: 'Yes',
                                   ) ??
                                       false;
                               if (didRequestSignOut == true) {
                                 try {
                                   EasyLoading.show(status: 'Logging Out');
                                   await ref
                                       .read(authRepositoryProvider)
                                       .updateToken(FirebaseAuth.instance.currentUser?.uid ?? '', '');
                                   await ref
                                       .read(authRepositoryProvider)
                                       .signOut();
                                   // MyApp.of(context).authService.authenticated = true;
                                   // onLoginCallback?.call(true);
                                   // AutoRouter.of(context).push(const DashboardRouter());
                                   EasyLoading.dismiss();
                                   context.replace('/login');
                                 } catch (err) {
                                   print(err);
                                   // debugPrint("Error :$err");
                                 }
                               }

                             },
                           ),
                         ),
                         Positioned(
                           top: appDimension.l7Y(x: size.width, y: size.height),
                           left: appDimension.l7X(x: size.width, y: size.height),
                           child: VerticalPipeline(
                             length: appDimension.l7Length(x: size.width, y: size.height),
                             name: "L7",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             singleFlow: true,
                           ),
                         ),
                         Positioned(
                           top: appDimension.v3Y(x: size.width, y: size.height),
                           left: appDimension.v3X(x: size.width, y: size.height),
                           child: SindhulpalchowkValveWidget(
                             size: size,
                             role: prefs.getUserRole(),
                             appDimension: appDimension,
                             showProgress: false,
                             valveId: "20",
                             valveModel: MotorModel(),
                             name: "Valve 20",
                           ),
                         ),

                         Positioned(
                           top: appDimension.l7Y(x: size.width, y: size.height),
                           left: appDimension.l7X(x: size.width, y: size.height),
                           child: VerticalPipeline(
                             length: appDimension.l7Length(x: size.width, y: size.height),
                             name: "L7",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             singleFlow: true,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l8Y(x: size.width, y: size.height),
                           left: appDimension.l8X(x: size.width, y: size.height),
                           child: VerticalPipeline(
                             length: appDimension.l8Length(x: size.width, y: size.height),
                             name: "L8",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isLeft: false,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.v4Y(x: size.width, y: size.height),
                           left: appDimension.v4X(x: size.width, y: size.height),
                           child: SindhulpalchowkValveWidget(
                             size: size,
                             role: prefs.getUserRole(),
                             appDimension: appDimension,
                             showProgress: false,
                             valveId: "20",
                             valveModel: MotorModel(),
                             name: "Valve 20",
                           ),
                         ),
                         Positioned(
                           top: appDimension.v5Y(x: size.width, y: size.height),
                           left: appDimension.v5X(x: size.width, y: size.height),
                           child: SindhulpalchowkValveWidget(
                             size: size,
                             role: prefs.getUserRole(),
                             appDimension: appDimension,
                             showProgress: false,
                             valveId: "20",
                             valveModel: MotorModel(),
                             name: "Valve 20",
                           ),
                         ),
                         Positioned(
                           top: appDimension.v6Y(x: size.width, y: size.height),
                           left: appDimension.v6X(x: size.width, y: size.height),
                           child: SindhulpalchowkValveWidget(
                             size: size,
                             role: prefs.getUserRole(),
                             appDimension: appDimension,
                             showProgress: false,
                             valveId: "20",
                             valveModel: MotorModel(),
                             name: "Valve 20",
                           ),
                         ),

                         Positioned(
                           top: appDimension.filterY(x: size.width, y: size.height),
                           left: appDimension.filterX(x: size.width, y: size.height),
                           child:Column(
                             children: [
                               Padding(
                                 padding: const EdgeInsets.all(8.0),
                                 child: Text("Slow Sand Filter"),
                               ),
                               Container(
                                 height: appDimension.filterHeight(x: size.width, y: size.height),
                                 width: appDimension.filterWidth(x: size.width, y: size.height),
                                 decoration: BoxDecoration(
                                   color: Colors.white,
                                   borderRadius: BorderRadius.circular(10),
                                   border: Border.all(color: Colors.black, width: 2),
                                 ),
                                 child: Image.asset(
                                   "assets/icons/slow_sand_filter.png",
                                   fit: BoxFit.fill,
                                 ),
                               ),
                             ],
                           ),
                         ),
                         Positioned(
                           top: appDimension.l81Y(x: size.width, y: size.height),
                           left: appDimension.l81X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l81Length(x: size.width, y: size.height),
                             name: "L81",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             leftFlow: true,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l82Y(x: size.width, y: size.height),
                           left: appDimension.l82X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l82Length(x: size.width, y: size.height),
                             name: "L82",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             leftFlow: true,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l83Y(x: size.width, y: size.height),
                           left: appDimension.l83X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l83Length(x: size.width, y: size.height),
                             name: "L83",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             leftFlow: true,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l10Y(x: size.width, y: size.height),
                           left: appDimension.l10X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l10Length(x: size.width, y: size.height),
                             name: "L10",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             leftFlow: true,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l11Y(x: size.width, y: size.height),
                           left: appDimension.l11X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l11Length(x: size.width, y: size.height),
                             name: "L11",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             leftFlow: true,
                             singleFlow: false,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l12Y(x: size.width, y: size.height),
                           left: appDimension.l12X(x: size.width, y: size.height),
                           child: VerticalPipeline(
                             length: appDimension.l12Length(x: size.width, y: size.height),
                             name: "L12",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             isLeft: false,
                             singleFlow: true,
                           ),
                         ),
                         Positioned(
                           top: appDimension.l13Y(x: size.width, y: size.height),
                           left: appDimension.l13X(x: size.width, y: size.height),
                           child: HorizontalPipeline(
                             length: appDimension.l13Length(x: size.width, y: size.height),
                             name: "L13",
                             isFlowing: true,
                             isTop: true,
                             isRight: false,
                             isBottom: false,
                             leftFlow: true,
                             singleFlow: true,
                           ),
                         ),

                         Positioned(
                           key: const ValueKey('D2'),
                           top: appDimension.d2Y(x: size.width, y: size.height),
                           left: appDimension.d2X(x: size.width, y: size.height),
                           child:RotatedDistributionWidget(name: "D2", height: 60, width: 100),
                         ),
                         Positioned(
                           key: const ValueKey('D3'),
                           top: appDimension.d3Y(x: size.width, y: size.height),
                           left: appDimension.d3X(x: size.width, y: size.height),
                           child:RotatedDistributionWidget(name: "D3", height: 60, width: 100),
                         ),

                         Positioned(
                           key: const ValueKey('S2'),
                           top: appDimension.s2Y(x: size.width, y: size.height),
                           left: appDimension.s2X(x: size.width, y: size.height),
                           child:  SingleSensorWidget(
                             listenerRequired: true,
                               sensorModel: SensorModel(),
                               sensorId: "11",
                               ),
                         ),
                         Positioned(
                           key: const ValueKey('S3'),
                           top: appDimension.s3Y(x: size.width, y: size.height),
                           left: appDimension.s3X(x: size.width, y: size.height),
                           child:  const ThreeSensorWidget(
                           sensorId: "12",),
                         ),



                       ],
                     ),
                   ),
                 ],
               );
            },
                unInitialized: ()=> Container(),
                error: (er){
                  return Container();
                },
                unauthorized: ()=>Container(),
                loading: ()=>LoadingIndicator())




          ),
        )
    );
  }

}