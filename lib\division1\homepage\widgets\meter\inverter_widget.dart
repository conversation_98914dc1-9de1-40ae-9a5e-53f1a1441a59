import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../model/motor_model.dart';
import '../../../provider/division_provider.dart';

class InverterWidget extends HookConsumerWidget {
  final double current;
  final double voltage;
  final double freq;
  final double currentThreshold;
  final String inverterId;

  const InverterWidget(
      {super.key,
      this.current = 0.0,
      this.voltage = 0.0,
      this.freq = 0.0,
      this.currentThreshold = 0.01,
      required this.inverterId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Calculate power (P = V × I)
    final power = voltage * current;

    final isActive = current > currentThreshold;

    return GestureDetector(
      onTap: () {
        // Navigate to inverter chart page
        context.push('/home/<USER>', extra: inverterId);
      },
      child: Container(
      height: 90,
      width: 130,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? Colors.green : Colors.grey,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isActive
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Title
            Text(
              'INVERTER',
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 8,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.0,
              ),
            ),

            // Data in two rows
            Column(
              children: [
                // First row: Current and Power
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildDataItem(
                      'CURRENT',
                      (current ?? 0.0).toStringAsFixed(2),
                      'A',
                      isActive,
                    ),
                    _buildDataItem(
                      'POWER',
                      power.toStringAsFixed(1),
                      'W',
                      isActive,
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                // Second row: Voltage and Frequency
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildDataItem(
                      'VOLTAGE',
                      (voltage ?? 0.0).toStringAsFixed(1),
                      'V',
                      isActive,
                    ),
                    _buildDataItem(
                      'FREQ',
                      (freq ?? 0.0).toStringAsFixed(1),
                      'Hz',
                      isActive,
                    ),
                  ],
                ),
              ],
            ),

            // Status indicator
            Container(
              width: double.infinity,
              height: 4,
              decoration: BoxDecoration(
                color: isActive ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _buildDataItem(
      String label, String value, String unit, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 6,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 1),
        Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 11,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(width: 1),
            Text(
              unit,
              style: TextStyle(
                color: isActive ? Colors.green[300] : Colors.grey[600],
                fontSize: 7,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class DischargeWidget extends HookConsumerWidget {
  final double current;
  final double voltage;
  final double freq;
  final double currentThreshold;

  const DischargeWidget(
      {super.key,
      this.current = 0.0,
      this.voltage = 0.0,
      this.freq = 0.0,
      this.currentThreshold = 0.01});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isActive = current > currentThreshold;

    return Container(
      height: 80,
      width: 100,
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? Colors.green : Colors.grey,
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: isActive
                ? Colors.green.withValues(alpha: 0.3)
                : Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Title
            Text(
              'Discharge Meter',
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 8,
                fontWeight: FontWeight.bold,
                letterSpacing: 1.0,
              ),
            ),

            // Data in two rows
            Column(
              children: [
                // First row: Current and Power
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildDataItem(
                      'Discharge',
                      (current ?? 0.0).toStringAsFixed(2),
                      'l/hr',
                      isActive,
                    ),
                  ],
                ),

                const SizedBox(height: 4),

                // Second row: Voltage and Frequency
              ],
            ),

            // Status indicator
            Container(
              width: double.infinity,
              height: 4,
              decoration: BoxDecoration(
                color: isActive ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataItem(
      String label, String value, String unit, bool isActive) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 6,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 1),
        Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              value,
              style: TextStyle(
                color: isActive ? Colors.green : Colors.grey,
                fontSize: 11,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
            const SizedBox(width: 1),
            Text(
              unit,
              style: TextStyle(
                color: isActive ? Colors.green[300] : Colors.grey[600],
                fontSize: 7,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
