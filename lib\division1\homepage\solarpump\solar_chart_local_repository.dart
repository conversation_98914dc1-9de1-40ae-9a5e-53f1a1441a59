import 'package:hive/hive.dart';
import 'package:si/division1/model/solar_chart_model.dart';

import '../../hive/hive_key.dart';
import 'model/hive_solar_history_model.dart';

abstract class ISolarChartHiveRepository {
  Future<void> insertSiteHistory(
      List<SolarChartFirestoreModel?> siteHistoryModel);

  Future<void> insertSiteHistoryifNotExists(
      List<SolarChartFirestoreModel?> siteHistoryModel);

  Future<List<HiveSolarHistoryModel>> getSiteHistoryHiveModel(int timeInMillis);

  int? isDataAvailable(int timeInMillis);

  void clearAll();
}

class SolarChartHiveRepository implements ISolarChartHiveRepository {
  final box = HiveKey.motorHistory;

  @override
  Future<List<HiveSolarHistoryModel>> getSiteHistoryHiveModel(
      int timeInMillis) async {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final targetDate = DateTime.fromMillisecondsSinceEpoch(timeInMillis);
    final list = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == targetDate.year &&
          elementDate.month == targetDate.month &&
          elementDate.day == targetDate.day;
    }).toList();
    return Future.value(list);
  }

  @override
  Future<void> insertSiteHistory(
      List<SolarChartFirestoreModel?> siteHistoryHiveModel) async {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    for (final model in siteHistoryHiveModel) {
      siteHistoryBox.add(HiveSolarHistoryModel(
          id: model?.id!,
          amp: model?.amps,
          volt: model?.volt,
          freq: model?.freq,
          time: model?.time));
    }
    return Future.value();
  }

  @override
  int? isDataAvailable(int timeInMillis) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final targetDate = DateTime.fromMillisecondsSinceEpoch(timeInMillis);
    final data = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == targetDate.year &&
          elementDate.month == targetDate.month &&
          elementDate.day == targetDate.day;
    }).toList();
    if (data.isNotEmpty) {
      return data.last.time!;
    } else {
      return null;
    }
  }

  bool isDateAvailable(int timeInMillis) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final targetDate = DateTime.fromMillisecondsSinceEpoch(timeInMillis);
    final data = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == targetDate.year &&
          elementDate.month == targetDate.month &&
          elementDate.day == targetDate.day;
    }).toList();
    return data.isNotEmpty;
  }

  @override
  void clearAll() {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    siteHistoryBox.clear();
  }

  @override
  Future<void> insertSiteHistoryifNotExists(
      List<SolarChartFirestoreModel?> siteHistoryModel) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    for (final model in siteHistoryModel) {
      if (model?.time != null) {
        if (!isDateAvailable(model!.time!)) {
          siteHistoryBox.add(HiveSolarHistoryModel(
              id: model.id,
              amp: model.amps,
              volt: model.volt,
              freq: model.freq,
              time: model.time));
        }
      }
    }
    return Future.value();
  }
}
