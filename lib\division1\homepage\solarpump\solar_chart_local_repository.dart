import 'package:hive/hive.dart';
import 'package:si/division1/model/solar_chart_model.dart';

import '../../hive/hive_key.dart';
import 'model/hive_solar_history_model.dart';

abstract class ISolarChartHiveRepository {
  Future<void> insertSiteHistory(
      List<SolarChartFirestoreModel?> siteHistoryModel);

  Future<void> insertSiteHistoryifNotExists(
      List<SolarChartFirestoreModel?> siteHistoryModel);

  Future<List<HiveSolarHistoryModel>> getSiteHistoryHiveModel(
      DateTime dateTime);

  DateTime? isDataAvailable(DateTime dateTime);

  void clearAll();
}

class SolarChartHiveRepository implements ISolarChartHiveRepository {
  final box = HiveKey.motorHistory;

  @override
  Future<List<HiveSolarHistoryModel>> getSiteHistoryHiveModel(
      DateTime dateTime) async {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final list = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == dateTime.year &&
          elementDate.month == dateTime.month &&
          elementDate.day == dateTime.day;
    }).toList();
    return Future.value(list);
  }

  @override
  Future<void> insertSiteHistory(
      List<SolarChartFirestoreModel?> siteHistoryHiveModel) async {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    for (final model in siteHistoryHiveModel) {
      siteHistoryBox.add(HiveSolarHistoryModel(
          id: model?.id!,
          amp: model?.amps,
          volt: model?.volt,
          freq: model?.freq,
          time: model?.time));
    }
    return Future.value();
  }

  @override
  DateTime? isDataAvailable(DateTime dateTime) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final data = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == dateTime.year &&
          elementDate.month == dateTime.month &&
          elementDate.day == dateTime.day;
    }).toList();
    if (data.isNotEmpty) {
      return DateTime.fromMillisecondsSinceEpoch(data.last.time!);
    } else {
      return null;
    }
  }

  bool isDateAvailable(DateTime dateTime) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    final data = siteHistoryBox.values.where((element) {
      if (element.time == null) return false;
      final elementDate = DateTime.fromMillisecondsSinceEpoch(element.time!);
      return elementDate.year == dateTime.year &&
          elementDate.month == dateTime.month &&
          elementDate.day == dateTime.day;
    }).toList();
    return data.isNotEmpty;
  }

  @override
  void clearAll() {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    siteHistoryBox.clear();
  }

  @override
  Future<void> insertSiteHistoryifNotExists(
      List<SolarChartFirestoreModel?> siteHistoryModel) {
    final siteHistoryBox = Hive.box<HiveSolarHistoryModel>(box);
    for (final model in siteHistoryModel) {
      if (model?.time != null) {
        final modelDate = DateTime.fromMillisecondsSinceEpoch(model!.time!);
        if (!isDateAvailable(modelDate)) {
          siteHistoryBox.add(HiveSolarHistoryModel(
              id: model.id,
              amp: model.amps,
              volt: model.volt,
              freq: model.freq,
              time: model.time));
        }
      }
    }
    return Future.value();
  }
}
