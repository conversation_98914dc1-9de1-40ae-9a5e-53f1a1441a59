import 'dart:convert';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:si/constants/string_hardcoded.dart';
import 'package:si/division1/router/division_router.dart';
import 'package:si/routing/kawasoki_app_router.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_web_plugins/url_strategy.dart';
import '../firebase_options.dart';
import '../provider/dashboard_provider.dart';
import '../services/locator.dart';
import '../services/shared_preferences_service.dart';

import 'hive/hive_key.dart';
import 'homepage/solarpump/model/hive_solar_history_model.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  if (kIsWeb) {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.talkhuweb);
  } else {
    await Firebase.initializeApp();
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
  }

  setupLocator();
  final sharedPreferences = await SharedPreferences.getInstance();
  await Hive.initFlutter();
  Hive.registerAdapter(HiveSolarHistoryModelAdapter());
  await Hive.openBox<HiveObject>('motorHistory');
  const FlutterSecureStorage secureStorage = FlutterSecureStorage(
      aOptions: AndroidOptions(encryptedSharedPreferences: true));
  var containsEncryptionKey = await secureStorage.containsKey(
      key: HiveKey.encryptionKey,
      aOptions: const AndroidOptions(encryptedSharedPreferences: true));
  if (!containsEncryptionKey) {
    var key = Hive.generateSecureKey();
    await secureStorage.write(
        key: HiveKey.encryptionKey,
        value: base64UrlEncode(key),
        aOptions: const AndroidOptions(encryptedSharedPreferences: true));
  }
  var encryptionKey = base64Url.decode((await secureStorage.read(
          key: HiveKey.encryptionKey,
          aOptions: const AndroidOptions(encryptedSharedPreferences: true))) ??
      '');
  await Hive.openBox(HiveKey.siBox,
      encryptionCipher: HiveAesCipher(encryptionKey));
  if (kIsWeb) {
    setUrlStrategy(PathUrlStrategy());
  }

  runApp(ProviderScope(overrides: [
    sharedPreferencesServiceProvider.overrideWithValue(
      SharedPreferencesService(sharedPreferences),
    ),
  ], child: MyApp()));
}

class MyApp extends StatefulHookConsumerWidget {
  MyApp({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => AppState();
}

class AppState extends ConsumerState<MyApp> {
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  void onBgMessage() async {
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      // handleNotification(fromBackground:true, message: initialMessage);
    }
  }

  void handleNotification(
      {bool fromBackground = false, required RemoteMessage message}) async {
    if (message.data != null) {
      if (message.data.containsKey('title') &&
          message.data.containsKey('body')) {
        ref
            .read(localNotificationProvider)
            .showNotifications(message.data['title'], message.data['body']);
      }
    }
  }

  @override
  void initState() {
    super.initState();
    if (!kIsWeb) {
      ref.read(fcmServiceProvider).requestPermission();
      ref.read(fcmServiceProvider).loadFcm();
      if (Platform.isAndroid) {
        _initLocalNotifications();
      }
    }
  }

  Future<void> _initLocalNotifications() async {
    AndroidInitializationSettings androidInitializationSettings =
        const AndroidInitializationSettings('@mipmap/launcher_icon');

    InitializationSettings initializationSettings = InitializationSettings(
      android: androidInitializationSettings,
    );
    flutterLocalNotificationsPlugin.initialize(initializationSettings,
        onDidReceiveNotificationResponse: (response) async {
      if (response.payload != null) {
        try {
          // handle notification on click
          final message = RemoteMessage(
              data: jsonDecode(response.payload!), notification: null);
          handleNotification(fromBackground: false, message: message);
        } catch (e) {}
      }
    });
  }

  Future<void> showLocalNotification(RemoteMessage message) async {
    try {
      final AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
              message.notification?.android?.channelId ??
                  'notification_channel_id'.hardcoded,
              'app_notification_channel_name'.hardcoded,
              channelDescription: 'mobile app notification'.hardcoded,
              importance: Importance.max,
              priority: Priority.high);
      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
      );
      await flutterLocalNotificationsPlugin.show(
        DateTime.now().millisecondsSinceEpoch ~/ 10000,
        message.notification?.title,
        message.notification?.body,
        notificationDetails,
        payload: jsonEncode(message.data),
      );
    } catch (e) {
      //log("Exception in showing notification : $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      if (!kIsWeb) {
        FirebaseMessaging.onMessage.listen((message) {
          print(message);
          if (message.data.containsKey('title') &&
              message.data.containsKey('body')) {
            ref
                .read(localNotificationProvider)
                .showNotifications(message.data['title'], message.data['body']);
          }
        });

        FirebaseMessaging.onMessageOpenedApp
            .listen((message) => handleNotification(message: message));
      }

      return;
    }, const []);

    return MaterialApp.router(
      builder: EasyLoading.init(),
      routerConfig: ref.read(divisionRouter),
    );
  }
}

/// A page that fades in an out.
class DivisionFadeTransitionPage extends CustomTransitionPage<void> {
  /// Creates a [FadeTransitionPage].
  DivisionFadeTransitionPage({
    required LocalKey super.key,
    required super.child,
  }) : super(
            transitionsBuilder: (BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation,
                    Widget child) =>
                FadeTransition(
                  opacity: animation.drive(_curveTween),
                  child: child,
                ));

  static final CurveTween _curveTween = CurveTween(curve: Curves.easeIn);
}
