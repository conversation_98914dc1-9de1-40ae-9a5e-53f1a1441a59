

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/solarpump/current_chart.dart';
import 'package:si/division1/homepage/solarpump/freq_chart.dart';
import 'package:si/division1/homepage/solarpump/solarchart_controller.dart';
import 'package:si/division1/homepage/solarpump/voltage_chart.dart';

import '../../../constants/app_sizes.dart';
import '../../../kawosoti/schema/widgets/date_forward_backward.dart';

class SolarChartPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartController = ref.watch(solarChartProvider("motorHistory"));
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          children: [
            DateForwardBackward(onDateChange: (date) {
              ref.read(solarChartProvider("motorHistory").notifier).getData(date);
            }),
            chartController.when(
              success: (data, message) {
                return Consumer(
                    builder: (context, ref, child) {

                      return Column(
                        children: [
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Current',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: CurrentChart(chartModel: data!,))
                          ),
                          gapH16,
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Frequency',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: FrequencyChart(chartModel: data,))
                          ),
                          gapH16,
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Text('Voltage',style: TextStyle(fontSize: kIsWeb ? 20.0 : 16.0,color: Colors.black),),
                          ),
                          Padding(
                              padding: const EdgeInsets.all(kIsWeb ? 16.0 : 8.0),
                              child: AspectRatio(
                                  aspectRatio: 2,
                                  child: VoltageChart(chartModel: data,))
                          ),
                          gapH16,

                        ],
                      );
                    }
                );
              },

              error: (er) {
                return Container();
              },
              unInitialized: () {
                return Container();
              },
              loading: () {
                return CircularProgressIndicator();
              }, unauthorized: () {
                return Container();
            },
            )
          ],
        ),
      ),
    );
  }
}
