


import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/solar_chart_model.dart';
import 'package:si/kawosoti/schema/model/chart_model.dart';

import '../../../utils/app_colors.dart';

class CurrentChart extends HookConsumerWidget {

  const CurrentChart({required this.chartModel});

  final SolarChartModel chartModel;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return LineChart(sampleData,duration: const Duration(milliseconds: 250),);
  }


  LineChartData get sampleData =>
      LineChartData(

        lineTouchData: lineTouchData,
        gridData: gridData,
        titlesData: titlesData,
        borderData: borderData,
        lineBarsData: lineBarsData,
        minX: chartModel.minX ?? 0,
        maxX: chartModel.maxX ?? 14,
        maxY: 500,
        minY: chartModel.minY ?? 0,
      );

  LineTouchData get lineTouchData =>
      LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          maxContentWidth: 100,
          tooltipBgColor: Colors.black,
          getTooltipItems: (touchedSpots) {
            return touchedSpots.map((LineBarSpot touchedSpot) {
              final textStyle = TextStyle(
                color: touchedSpot.bar.gradient?.colors[0] ??
                    touchedSpot.bar.color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              );
              return LineTooltipItem(
                '${touchedSpot.y.toStringAsFixed(2)} at ${getHrs(touchedSpot.x)}',
                textStyle,
              );
            }).toList();
          },
        ),
        handleBuiltInTouches: true,
        getTouchLineStart: (data, index) => 0,
      );

  FlTitlesData get titlesData =>
      FlTitlesData(
        bottomTitles: AxisTitles(
          axisNameWidget: Text('Time'),
          sideTitles: bottomTitles,
        ),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          axisNameWidget: Text('Value'),
          sideTitles: leftTitles(),
        ),
      );

  List<LineChartBarData> get lineBarsData => [
    lineChartBart1Data,

  ];

  LineChartBarData get lineChartBart1Data =>
      LineChartBarData(
        isCurved: true,
        color: AppColors.contentColorGreen,
        barWidth: 4,
        isStrokeCapRound: true,
        dotData: const FlDotData(show: true),
        belowBarData: BarAreaData(show: true),
        spots: chartModel.ampList!,
      );

  Widget bottomTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 16,
    );
    Widget text;
    text = Text(getHrs(value));

    return SideTitleWidget(
      axisSide: meta.axisSide,
      space: 10,
      child: text,
    );
  }
  String getHrs(double min) {
    final int hour = (min ~/ 60);
    final int minutes = min.toInt() % 60;
    return '${hour.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2,'0')}';
  }

  SideTitles get bottomTitles =>
      SideTitles(
        showTitles: true,
        reservedSize: 32,
        interval: 120,
        getTitlesWidget: bottomTitleWidgets,
      );

  FlGridData get gridData => const FlGridData(show: true);

  FlBorderData get borderData =>
      FlBorderData(
        show: true,
        border: Border(
            bottom:
            BorderSide(color: Colors.black.withOpacity(0.2), width: 4),
            left: const BorderSide(color: Colors.black),
            right: const BorderSide(color: Colors.transparent),
            top: const BorderSide(color: Colors.transparent)
        ),
      );

  SideTitles leftTitles() =>
      SideTitles(
        getTitlesWidget: leftTitleWidgets,
        showTitles: true,
        interval: 50,
        reservedSize: 40,
      );

  Widget leftTitleWidgets(double value, TitleMeta meta) {
    const style = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );
    String text;
    text = value.toString();

    return Text(text, style: style, textAlign: TextAlign.center);
  }

}