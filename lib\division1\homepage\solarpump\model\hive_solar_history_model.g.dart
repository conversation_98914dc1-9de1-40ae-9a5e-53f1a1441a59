// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hive_solar_history_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class HiveSolarHistoryModelAdapter extends TypeAdapter<HiveSolarHistoryModel> {
  @override
  final int typeId = 0;

  @override
  HiveSolarHistoryModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return HiveSolarHistoryModel(
      id: fields[0] as String?,
      amp: fields[1] as int?,
      volt: fields[2] as int?,
      freq: fields[3] as int?,
      time: fields[4] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, HiveSolarHistoryModel obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.amp)
      ..writeByte(2)
      ..write(obj.volt)
      ..writeByte(3)
      ..write(obj.freq)
      ..writeByte(4)
      ..write(obj.time);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HiveSolarHistoryModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
