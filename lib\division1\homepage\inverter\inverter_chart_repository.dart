import 'package:si/division1/model/inverter_chart_model.dart';
import 'package:si/services/firestore_services.dart';

abstract class IInverterChartRepository {
  Future<List<InverterChartFirestoreModel?>> getInverterChartData({
    required String inverterId,
    required DateTime date,
  });
}

class InverterChartRepository implements IInverterChartRepository {
  final FirestoreService _firestoreService = FirestoreService.instance;

  @override
  Future<List<InverterChartFirestoreModel?>> getInverterChartData({
    required String inverterId,
    required DateTime date,
  }) async {
    try {
      // Format date to get data for the specific day
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final startTimestamp = startOfDay.millisecondsSinceEpoch;
      final endTimestamp = endOfDay.millisecondsSinceEpoch;

      // For demo purposes, generate sample data if no real data is available
      // In production, replace this with actual Firestore query
      try {
        final List<InverterChartFirestoreModel?> data = await _firestoreService
            .collectionFuture<InverterChartFirestoreModel>(
          path: 'inverterData', // Adjust collection path as needed
          builder: (data, documentId) =>
              InverterChartFirestoreModel.fromMap(data, documentId),
          queryBuilder: (query) => query
              .where('inverterId', isEqualTo: inverterId)
              .where('time', isGreaterThanOrEqualTo: startTimestamp)
              .where('time', isLessThanOrEqualTo: endTimestamp)
              .orderBy('time'),
        );

        if (data.isNotEmpty) {
          return data;
        }
      } catch (e) {
        print('Firestore query failed, generating demo data: $e');
      }

      // Generate demo data for testing
      return _generateDemoData(inverterId, startOfDay);
    } catch (e) {
      print('Error fetching inverter chart data: $e');
      return _generateDemoData(inverterId, DateTime.now());
    }
  }

  List<InverterChartFirestoreModel> _generateDemoData(
      String inverterId, DateTime date) {
    final List<InverterChartFirestoreModel> demoData = [];

    // Generate data points every 30 minutes for 24 hours
    for (int hour = 0; hour < 24; hour++) {
      for (int minute = 0; minute < 60; minute += 30) {
        final timestamp =
            DateTime(date.year, date.month, date.day, hour, minute)
                .millisecondsSinceEpoch;

        // Generate realistic inverter data
        final current = 5.0 +
            (hour > 6 && hour < 18 ? 15.0 : 0.0) +
            (DateTime.now().millisecond % 100) / 100.0;
        final voltage = 220.0 + (DateTime.now().millisecond % 20) - 10.0;
        final frequency = 50.0 + (DateTime.now().millisecond % 10) / 10.0 - 0.5;
        final power = current * voltage;

        demoData.add(InverterChartFirestoreModel(
          id: '${inverterId}_${timestamp}',
          current: current,
          voltage: voltage,
          frequency: frequency,
          power: power,
          time: timestamp,
          inverterId: inverterId,
        ));
      }
    }

    return demoData;
  }
}
