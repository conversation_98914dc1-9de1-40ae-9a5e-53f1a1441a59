name: si
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependen
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  cached_network_image: ^3.2.3

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  go_router: ^13.0.1

  # firebase
  firebase_database: ^11.3.2
  firebase_messaging: ^15.2.2
  firebase_core: ^3.11.0
  cloud_firestore: ^5.6.3
  firebase_auth: ^5.4.2
  font_awesome_flutter: ^10.0.0
  equatable: ^2.0.5
  geolocator: ^13.0.1
  # rivverpod
  riverpod: 2.4.0
  scrollable_text: ^0.0.2
  flutter_riverpod: 2.4.0
  hooks_riverpod: 2.4.0
  flutter_hooks: 0.20.5

  google_sign_in: ^6.1.5
  shared_preferences: ^2.2.1

  syncfusion_flutter_gauges: ^26.2.11
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  freezed: ^2.4.1

  adaptive_navigation: ^0.0.9
  hive: 2.2.3
  hive_flutter: 1.1.0
  flutter_secure_storage: ^9.0.0
  simple_chips_input: ^1.2.0
  flutter_easyloading: ^3.0.5
  get_it: 7.6.4
  flutter_local_notifications: 17.2.2
  permission_handler: 11.0.0
  loading_animation_widget: ^1.2.0+2
  digital_lcd_number: ^0.1.2

  flutter_spinkit: ^5.1.0
  flutter_switch: ^0.3.2
  collection: ^1.15.0

  flutter_native_splash: ^2.3.2
  flutter_launcher_icons: ^0.13.1
  water_bottle: ^0.0.7
  graphite: ^1.1.2
  touchable: ^1.0.2
  fl_chart: ^0.64.0
  excel: ^4.0.0
  msh_checkbox: ^2.0.1
  wave: ^0.2.2
  responsive_grid: ^2.4.4
  mobile_scanner: ^3.5.5
  url_launcher: ^6.1.10
  progress_stepper: ^3.1.0
  firebase_storage: ^12.4.0
  percent_indicator: ^4.2.4
  firebase_ui_firestore: ^1.7.1
  flutter_fortune_wheel: ^1.3.2
  #device info
  device_info_plus: 9.1.1
  package_info_plus: ^8.1.3
  #image picker and cropper
  image_picker: ^1.0.4
  flutter_picker: 2.1.0
  interval_time_picker: ^3.0.3+9
  text_scroll: ^0.2.0
  day_picker: ^2.3.0
  circular_progress_stack: ^0.0.4
  advanced_chips_input: ^0.1.5

dev_dependencies:
  flutter_test:
    sdk: flutter

  build_runner: ^2.4.14
  hive_generator: ^2.0.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  json_serializable: ^6.9.0
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/icons/


flutter_icons:
  android: "ic_launcher"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/icons/ic_logo.png"

flutter_native_splash:
  color: "#FFFFFF"
  android_12:
  image: assets/icons/ic_logo_splash.png

  # dart run flutter_launcher_icons:main
  # dart run flutter_native_splash:create
  # flutter build apk --release --flavor chitwan -t lib/division/main_division.dart

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
