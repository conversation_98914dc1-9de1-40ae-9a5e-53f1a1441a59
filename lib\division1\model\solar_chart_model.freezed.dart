// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'solar_chart_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SolarChartFirestoreModel _$SolarChartFirestoreModelFromJson(
    Map<String, dynamic> json) {
  return _SolarChartFirestoreModel.fromJson(json);
}

/// @nodoc
mixin _$SolarChartFirestoreModel {
  int? get amps => throw _privateConstructorUsedError;
  String? get id => throw _privateConstructorUsedError;
  int? get volt => throw _privateConstructorUsedError;
  int? get siteId => throw _privateConstructorUsedError;
  int? get motorId => throw _privateConstructorUsedError;
  int? get freq => throw _privateConstructorUsedError;
  int? get rssi => throw _privateConstructorUsedError;
  int? get time => throw _privateConstructorUsedError;

  /// Serializes this SolarChartFirestoreModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SolarChartFirestoreModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SolarChartFirestoreModelCopyWith<SolarChartFirestoreModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SolarChartFirestoreModelCopyWith<$Res> {
  factory $SolarChartFirestoreModelCopyWith(SolarChartFirestoreModel value,
          $Res Function(SolarChartFirestoreModel) then) =
      _$SolarChartFirestoreModelCopyWithImpl<$Res, SolarChartFirestoreModel>;
  @useResult
  $Res call(
      {int? amps,
      String? id,
      int? volt,
      int? siteId,
      int? motorId,
      int? freq,
      int? rssi,
      int? time});
}

/// @nodoc
class _$SolarChartFirestoreModelCopyWithImpl<$Res,
        $Val extends SolarChartFirestoreModel>
    implements $SolarChartFirestoreModelCopyWith<$Res> {
  _$SolarChartFirestoreModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SolarChartFirestoreModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amps = freezed,
    Object? id = freezed,
    Object? volt = freezed,
    Object? siteId = freezed,
    Object? motorId = freezed,
    Object? freq = freezed,
    Object? rssi = freezed,
    Object? time = freezed,
  }) {
    return _then(_value.copyWith(
      amps: freezed == amps
          ? _value.amps
          : amps // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      volt: freezed == volt
          ? _value.volt
          : volt // ignore: cast_nullable_to_non_nullable
              as int?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      motorId: freezed == motorId
          ? _value.motorId
          : motorId // ignore: cast_nullable_to_non_nullable
              as int?,
      freq: freezed == freq
          ? _value.freq
          : freq // ignore: cast_nullable_to_non_nullable
              as int?,
      rssi: freezed == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SolarChartFirestoreModelImplCopyWith<$Res>
    implements $SolarChartFirestoreModelCopyWith<$Res> {
  factory _$$SolarChartFirestoreModelImplCopyWith(
          _$SolarChartFirestoreModelImpl value,
          $Res Function(_$SolarChartFirestoreModelImpl) then) =
      __$$SolarChartFirestoreModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? amps,
      String? id,
      int? volt,
      int? siteId,
      int? motorId,
      int? freq,
      int? rssi,
      int? time});
}

/// @nodoc
class __$$SolarChartFirestoreModelImplCopyWithImpl<$Res>
    extends _$SolarChartFirestoreModelCopyWithImpl<$Res,
        _$SolarChartFirestoreModelImpl>
    implements _$$SolarChartFirestoreModelImplCopyWith<$Res> {
  __$$SolarChartFirestoreModelImplCopyWithImpl(
      _$SolarChartFirestoreModelImpl _value,
      $Res Function(_$SolarChartFirestoreModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of SolarChartFirestoreModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? amps = freezed,
    Object? id = freezed,
    Object? volt = freezed,
    Object? siteId = freezed,
    Object? motorId = freezed,
    Object? freq = freezed,
    Object? rssi = freezed,
    Object? time = freezed,
  }) {
    return _then(_$SolarChartFirestoreModelImpl(
      amps: freezed == amps
          ? _value.amps
          : amps // ignore: cast_nullable_to_non_nullable
              as int?,
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String?,
      volt: freezed == volt
          ? _value.volt
          : volt // ignore: cast_nullable_to_non_nullable
              as int?,
      siteId: freezed == siteId
          ? _value.siteId
          : siteId // ignore: cast_nullable_to_non_nullable
              as int?,
      motorId: freezed == motorId
          ? _value.motorId
          : motorId // ignore: cast_nullable_to_non_nullable
              as int?,
      freq: freezed == freq
          ? _value.freq
          : freq // ignore: cast_nullable_to_non_nullable
              as int?,
      rssi: freezed == rssi
          ? _value.rssi
          : rssi // ignore: cast_nullable_to_non_nullable
              as int?,
      time: freezed == time
          ? _value.time
          : time // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SolarChartFirestoreModelImpl implements _SolarChartFirestoreModel {
  const _$SolarChartFirestoreModelImpl(
      {this.amps,
      this.id,
      this.volt,
      this.siteId,
      this.motorId,
      this.freq,
      this.rssi,
      this.time});

  factory _$SolarChartFirestoreModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SolarChartFirestoreModelImplFromJson(json);

  @override
  final int? amps;
  @override
  final String? id;
  @override
  final int? volt;
  @override
  final int? siteId;
  @override
  final int? motorId;
  @override
  final int? freq;
  @override
  final int? rssi;
  @override
  final int? time;

  @override
  String toString() {
    return 'SolarChartFirestoreModel(amps: $amps, id: $id, volt: $volt, siteId: $siteId, motorId: $motorId, freq: $freq, rssi: $rssi, time: $time)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SolarChartFirestoreModelImpl &&
            (identical(other.amps, amps) || other.amps == amps) &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.volt, volt) || other.volt == volt) &&
            (identical(other.siteId, siteId) || other.siteId == siteId) &&
            (identical(other.motorId, motorId) || other.motorId == motorId) &&
            (identical(other.freq, freq) || other.freq == freq) &&
            (identical(other.rssi, rssi) || other.rssi == rssi) &&
            (identical(other.time, time) || other.time == time));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, amps, id, volt, siteId, motorId, freq, rssi, time);

  /// Create a copy of SolarChartFirestoreModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SolarChartFirestoreModelImplCopyWith<_$SolarChartFirestoreModelImpl>
      get copyWith => __$$SolarChartFirestoreModelImplCopyWithImpl<
          _$SolarChartFirestoreModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SolarChartFirestoreModelImplToJson(
      this,
    );
  }
}

abstract class _SolarChartFirestoreModel implements SolarChartFirestoreModel {
  const factory _SolarChartFirestoreModel(
      {final int? amps,
      final String? id,
      final int? volt,
      final int? siteId,
      final int? motorId,
      final int? freq,
      final int? rssi,
      final int? time}) = _$SolarChartFirestoreModelImpl;

  factory _SolarChartFirestoreModel.fromJson(Map<String, dynamic> json) =
      _$SolarChartFirestoreModelImpl.fromJson;

  @override
  int? get amps;
  @override
  String? get id;
  @override
  int? get volt;
  @override
  int? get siteId;
  @override
  int? get motorId;
  @override
  int? get freq;
  @override
  int? get rssi;
  @override
  int? get time;

  /// Create a copy of SolarChartFirestoreModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SolarChartFirestoreModelImplCopyWith<_$SolarChartFirestoreModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
