

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/model/motor_setting_model.dart';
import 'package:si/division1/provider/division_provider.dart';
import 'package:si/kawosoti/schema/model/schema_model.dart';
import 'package:si/services/shared_preferences_service.dart';
import 'package:si/utils/constants.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart';

import '../../../../constants/app_sizes.dart';
import '../../../model/division_setting_model.dart';
import '../../../model/motor_model.dart';

class SingleSensorWidget extends HookConsumerWidget{

  final SensorModel? sensorModel;
  final bool isPortrait;
  final String sensorId;
  final bool listenerRequired;

  const SingleSensorWidget({  required this.sensorModel,
    this.sensorId = "10",this.isPortrait = false, this.listenerRequired = true});
  @override
  Widget build(BuildContext context, WidgetRef ref) {

    final sensorState = useState<SensorModel>(SensorModel());

    if(listenerRequired){
      ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider(sensorId),
              (previous, next) {
            if (next.asData?.value.snapshot.value != null) {
              final datasnapshot = next.asData?.value.snapshot.value as Map;
              sensorState.value = SensorModel(
                tur: datasnapshot['tur'],
                time: datasnapshot['time'],
                cum_flow: datasnapshot['cum_flow']!=null ? datasnapshot["cum_flow"] : 0.0,
                flow: datasnapshot['flow']!=null ? datasnapshot["flow"] : 0.0,);
            }
          });
    }


    return Container(
      height: isPortrait ? 80 : 100,
      width: isPortrait ?  70 : 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: Column(
        children: [
          Container(
            height: isPortrait ? 40 : 50,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("Turbidity", style: TextStyle(color: Colors.white)),
                Text(listenerRequired ? "${sensorState.value.tur?.toStringAsFixed(2)} NTU" :
                "${sensorModel?.tur?.toStringAsFixed(2)} NTU", style: const TextStyle(color: Colors.white, fontSize: 10)),
              ],
            ),
          ),
          Container(
            height: 1,
            color: Colors.black,
            width: double.infinity,
          ),
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child:Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: isPortrait ? 12 : 20, // Equal width and height for a perfect circle
                    height: isPortrait ? 12 : 20,
                    decoration:  BoxDecoration(
                      color: getTurbidityColor(listenerRequired ? (sensorState.value?.tur ?? 0.02) : (sensorModel?.tur ?? 0.3)), // Background color
                      shape: BoxShape.circle, // Makes the container circular
                    ),
                  )
                ],
              ),
            ),
          ),
        ],
      )
    );
  }

}


class TurbiditySensorWidget extends HookConsumerWidget{


  final bool isPortrait;
  final String sensorId;
  final SensorSettingModel? setting;
  final double width;


  const TurbiditySensorWidget({this.isPortrait =  false, this.sensorId = "10",
    this.setting, this.width = 600});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sensorState = useState<SensorModel>(SensorModel());

    final prefs = ref.read(sharedPreferencesServiceProvider);
    ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider(sensorId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            sensorState.value = SensorModel(
              tur: datasnapshot['tur'],
              time: datasnapshot['time'],
            );
          }
        });
    return Column(
      children: [
        Container(
            height: isPortrait ? 80  : 120,
            width: isPortrait ? 60 : 80,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black, width: 2),
            ),
            child: Column(
              children: [
                Container(
                  height: isPortrait ? 40 : 60,
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      setting!=null ? Text("Tur: ${double.parse((sensorState.value.tur ??  0.0).plus(setting?.tos ?? 0.0)).offsetTur(prefs.getUserRole()).toStringAsFixed(2)} NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12))
                          : sensorId != "32" ? Text("Tur: ${sensorState.value.tur?.offsetTur(prefs.getUserRole()).toStringAsFixed(2)} NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)) : Text("Tur: ---- NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                    ],
                  ),
                ),
                Container(
                  height: isPortrait ? 0.5 : 1,
                  color: Colors.black,
                  width: double.infinity,
                ),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(8),
                        bottomRight: Radius.circular(8),
                      ),
                    ),
                    child:Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width:isPortrait ? 8  : 20, // Equal width and height for a perfect circle
                              height:isPortrait ? 8  : 20,
                              decoration:  BoxDecoration(
                                color:  setting!=null ? getTurbidityColor(double.parse((sensorState.value.tur ??  0.0).plus(setting?.tos ?? 0.0)).offsetTur(prefs.getUserRole()))
                                    : getTurbidityColor(sensorState.value?.tur?.offsetTur(prefs.getUserRole()) ?? 0.3), // Background color
                                shape: BoxShape.circle, // Makes the container circular
                              ),
                            ),
                            gapH8,
                            Text("Tur", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
        ),
       /* gapH16,
        Container(
          height: 80,
            width: 80,
            child: _buildRadialTextPointer(sensorState.value.tur ?? 0.0))*/
      ],
    );
  }

}


class ThreeSensorWidget extends HookConsumerWidget{

  final bool isPortrait;
  final String sensorId;
  final SensorSettingModel? setting;

  const ThreeSensorWidget({ this.isPortrait =  false, this.sensorId = "10", this.setting});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sensorState = useState<SensorModel>(SensorModel());
    final prefs = ref.read(sharedPreferencesServiceProvider);
    ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider(sensorId),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            sensorState.value = SensorModel(
              tur: datasnapshot['tur'],
              time: datasnapshot['time'],
              cl: datasnapshot['cl'],
              ph: datasnapshot['ph'],
              );
          }
        });
    return Container(
        height: isPortrait ? 80  : 120,
        width: isPortrait ? 70 : 100,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.black, width: 2),
        ),
        child: Column(
          children: [
            Container(
              height: isPortrait ? 40 : 60,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  setting!=null ? Text("Tur: ${double.parse((sensorState.value.tur ??  0.0).plus(setting?.tos ?? 0.0)).offsetTur(prefs.getUserRole()).toStringAsFixed(2)} NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12))
                  : Text("Tur: ${sensorState.value.tur?.offsetTur(prefs.getUserRole()).toStringAsFixed(2)} NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                  setting!=null ? Text("Ph: ${double.parse((sensorState.value.ph ??  0.0).plus(setting?.phos ?? 0.0)).offsetPh(prefs.getUserRole()).toStringAsFixed(2)}", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12))
                      : Text("Ph: ${sensorState.value.ph?.offsetPh(prefs.getUserRole()).toStringAsFixed(2)}", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                  setting!=null ? Text("Cl: ${double.parse((sensorState.value.cl ??  0.0).scale(setting?.clscale ?? 1).plus(setting?.clos ?? 0.0)).offsetCl(prefs.getUserRole()).toStringAsFixed(2)} NTU", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12))
                      : Text("Cl: ${sensorState.value.cl?.offsetCl(prefs.getUserRole()).toStringAsFixed(2)}", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                ],
              ),
            ),
            Container(
              height: isPortrait ? 0.5 : 1,
              color: Colors.black,
              width: double.infinity,
            ),
            Expanded(
              child: Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child:Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width:isPortrait ? 8  : 20, // Equal width and height for a perfect circle
                          height:isPortrait ? 8  : 20,
                          decoration:  BoxDecoration(
                            color: setting!=null ? getTurbidityColor(double.parse((sensorState.value.tur ??  0.0).plus(setting?.tos ?? 0.0)).offsetTur(prefs.getUserRole()))
                             : getTurbidityColor(sensorState.value?.tur?.offsetTur(prefs.getUserRole()) ?? 0.3), // Background color
                            shape: BoxShape.circle, // Makes the container circular
                          ),
                        ),
                        gapH8,
                        Text("Tur", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 : 12)),
                      ],
                    ),Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: isPortrait ? 8  : 20, // Equal width and height for a perfect circle
                          height: isPortrait ? 8  : 20,
                          decoration:  BoxDecoration(
                            color: setting!=null ? getPh(double.parse((sensorState.value.ph ??  0.0).plus(setting?.phos ?? 0.0)))
                                : getPh(sensorState.value?.ph ?? 7.3), // Background color
                            shape: BoxShape.circle, // Makes the container circular
                          ),
                        ),
                        gapH8,
                        Text("pH", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 :12)),
                      ],
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: isPortrait ? 8  : 20, // Equal width and height for a perfect circle
                          height: isPortrait ? 8  : 20,
                          decoration:  BoxDecoration(
                            color: setting!=null ?  getChlorine(double.parse((sensorState.value.cl ??  0.0).scale(setting?.clscale ?? 1).plus(setting?.clos ?? 0.0)).offsetCl(prefs.getUserRole()))  :
                            getChlorine(sensorState.value?.cl?.offsetCl(prefs.getUserRole()) ?? 0.02), // Background color
                            shape: BoxShape.circle, // Makes the container circular
                          ),
                        ),
                        gapH8,
                        Text("Cl", style: TextStyle(color: Colors.white, fontSize: isPortrait ? 8 :12)),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        )
    );
  }

}

Color getTurbidityColor(double turbidity){
      if (turbidity! < 5) {
        return Colors.green;
      } else if (turbidity < 15) {
        return Colors.yellow;
        //return Colors.yellow;
      } else if(turbidity < 25){
        return Colors.orange;
       // return Colors.red;
      }else {
        return Colors.red;
      }
}

Color getChlorine(double cl){
  if (cl! >= 0.01 && cl < 0.3) {
    return Colors.green;
  }  else {
    return Colors.yellow;
    //return Colors.yellow;
  }
}

Color getPh(double ph){
  if(ph < 4 && ph > 10){
    return Colors.red;
  } else if (ph <= 5.5 && ph > 8.5) {
    return Colors.yellow;
  } else {
    return Colors.green;
  }
}

SfRadialGauge _buildRadialTextPointer(double turbidity) {
  return SfRadialGauge(
    axes: <RadialAxis>[
      RadialAxis(
          showAxisLine: false,
          showLabels: false,
          showTicks: false,
          startAngle: 180,
          endAngle: 360,
          maximum: 20,
          canScaleToFit: false,
          radiusFactor: 0.79,
          pointers:  <GaugePointer>[
            NeedlePointer(
                needleEndWidth: 5,
                needleLength: 0.7,
                value: turbidity,
                knobStyle: KnobStyle(knobRadius: 0)),
          ],
          ranges: <GaugeRange>[
            GaugeRange(
                startValue: 0,
                endValue: 3,
                startWidth: 0.45,
                endWidth: 0.45,
                sizeUnit: GaugeSizeUnit.factor,
                color: const Color(0xFF64BE00)),
            GaugeRange(
                startValue: 3,
                endValue: 5,
                startWidth: 0.45,
                sizeUnit: GaugeSizeUnit.factor,
                endWidth: 0.45,
                color: const Color(0xFF8BE724)
            ),
            GaugeRange(
                startValue: 5,
                endValue: 7.5,
                startWidth: 0.45,
                sizeUnit: GaugeSizeUnit.factor,
                endWidth: 0.45,
                color: const Color(0xFFFFDF10)),
            GaugeRange(
                startValue: 7.5,
                endValue: 10,
                startWidth: 0.45,
                sizeUnit: GaugeSizeUnit.factor,
                endWidth: 0.45,
                color: const Color(0xFFFFBA00)),
            GaugeRange(
                startValue: 10,
                endValue: 15,
                sizeUnit: GaugeSizeUnit.factor,
                startWidth: 0.45,
                endWidth: 0.45,
                color: const Color(0xFFFF4100)),
            GaugeRange(
                startValue: 15,
                endValue: 20,
                startWidth: 0.45,
                endWidth: 0.45,
                sizeUnit: GaugeSizeUnit.factor,
                color: const Color(0xFFDD3800)),
          ]),
      RadialAxis(
        showAxisLine: false,
        showLabels: false,
        showTicks: false,
        startAngle: 180,
        endAngle: 360,
        maximum: 120,
        radiusFactor: 0.85,
        canScaleToFit: false,
        pointers: <GaugePointer>[
          const MarkerPointer(
              markerType: MarkerType.text,
              text: 'G',
              value: 20.5,
              textStyle: GaugeTextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 8,
                  fontFamily: 'Times'),
              offsetUnit: GaugeSizeUnit.factor,
              markerOffset: -0.12),
          MarkerPointer(
              markerType: MarkerType.text,
              text: 'A',
              value: 60.5,
              textStyle: GaugeTextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 8,
                  fontFamily: 'Times'),
              offsetUnit: GaugeSizeUnit.factor,
              markerOffset: -0.12),
          MarkerPointer(
              markerType: MarkerType.text,
              text: 'P',
              value: 100.5,
              textStyle: GaugeTextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize:  8,
                  fontFamily: 'Times'),
              offsetUnit: GaugeSizeUnit.factor,
              markerOffset: -0.12)
        ],
      ),
    ],
  );
}
