


import 'package:hive/hive.dart';
part 'hive_solar_history_model.g.dart';

@HiveType(typeId: 0)
class HiveSolarHistoryModel extends HiveObject {
  @HiveField(0)
  String? id;

  @HiveField(1)
  int? amp;

  @HiveField(2)
  int? volt;

  @HiveField(3)
  int? freq;

  @HiveField(4)
  int? time;

  @HiveField(5)
  DateTime? date;

  HiveSolarHistoryModel({
    this.id,
    this.amp,
    this.volt,
    this.freq,
    this.time,
    this.date,
  });
}