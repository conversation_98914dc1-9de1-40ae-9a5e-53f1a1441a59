import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:si/division1/homepage/inverter/inverter_chart_repository.dart';
import 'package:si/division1/model/inverter_chart_model.dart';

import '../../../constants/state_model.dart';

final inverterChartRepositoryProvider = Provider<InverterChartRepository>((ref) {
  return InverterChartRepository();
});

final inverterChartControllerProvider = StateNotifierProvider.family<
    InverterChartController, Resource<InverterChartModel>, String>(
  (ref, inverterId) {
    final repository = ref.watch(inverterChartRepositoryProvider);
    return InverterChartController(repository, inverterId);
  },
);

class InverterChartController extends StateNotifier<Resource<InverterChartModel>> {
  final InverterChartRepository repository;
  final String inverterId;

  InverterChartController(this.repository, this.inverterId)
      : super(Resource.unInitialized());

  Future<void> getInverterChartData({required DateTime dateTime}) async {
    state = Resource.loading();

    try {
      final response = await repository.getInverterChartData(
        inverterId: inverterId,
        date: dateTime,
      );

      if (response.isEmpty) {
        state = Resource.error('No data available for the selected date');
        return;
      }

      // Initialize lists for chart data
      List<FlSpot> currentList = [];
      List<FlSpot> voltageList = [];
      List<FlSpot> frequencyList = [];
      List<FlSpot> powerList = [];

      // Process the data and convert to chart points
      for (int i = 0; i < response.length; i++) {
        final dataPoint = response[i]!;
        final timeInMins = getMinsFromTimestamp(dataPoint.time!);

        currentList.add(FlSpot(
            timeInMins, double.parse((dataPoint.current ?? 0).toStringAsFixed(2))));
        voltageList.add(FlSpot(
            timeInMins, double.parse((dataPoint.voltage ?? 0).toStringAsFixed(2))));
        frequencyList.add(FlSpot(
            timeInMins, double.parse((dataPoint.frequency ?? 0).toStringAsFixed(2))));
        powerList.add(FlSpot(
            timeInMins, double.parse((dataPoint.power ?? 0).toStringAsFixed(2))));
      }

      // Calculate min/max values for chart scaling
      double minX = currentList.isNotEmpty ? currentList.first.x : 0;
      double maxX = currentList.isNotEmpty ? currentList.last.x : 1440; // 24 hours in minutes

      final chartModel = InverterChartModel(
        currentList: currentList,
        voltageList: voltageList,
        frequencyList: frequencyList,
        powerList: powerList,
        minX: minX,
        maxX: maxX,
        minY: 0,
        maxY: 500, // Adjust based on your data range
      );

      state = Resource.success(chartModel, 'Data loaded successfully');
    } catch (e) {
      state = Resource.error('Failed to load inverter data: ${e.toString()}');
    }
  }

  double getMinsFromTimestamp(int timestamp) {
    // Convert timestamp (milliseconds since epoch) to DateTime
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // Return minutes from start of day (0-1439)
    return double.parse((date.hour * 60 + date.minute).toString());
  }
}
