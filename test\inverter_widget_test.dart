import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/widgets/meter/inverter_widget.dart';

void main() {
  group('InverterWidget Tests', () {
    testWidgets('InverterWidget displays correctly with default values',
        (WidgetTester tester) async {
      // Build the widget wrapped in ProviderScope for Riverpod
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InverterWidget(inverterId: "test"),
            ),
          ),
        ),
      );

      // Verify that the widget displays the title
      expect(find.text('INVERTER'), findsOneWidget);

      // Verify that all parameter labels are displayed
      expect(find.text('CURRENT'), findsOneWidget);
      expect(find.text('POWER'), findsOneWidget);
      expect(find.text('VOLTAGE'), findsOneWidget);
      expect(find.text('FREQ'), findsOneWidget);

      // Verify that units are displayed
      expect(find.text('A'), findsOneWidget);
      expect(find.text('W'), findsOneWidget);
      expect(find.text('V'), findsOneWidget);
      expect(find.text('Hz'), findsOneWidget);

      // Verify that default values are displayed
      expect(find.text('0.00'), findsOneWidget); // Current
      expect(
          find.text('0.0'), findsNWidgets(3)); // Power, voltage, and frequency
    });

    testWidgets('InverterWidget has correct styling',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InverterWidget(inverterId: "test"),
            ),
          ),
        ),
      );

      // Find the main container
      final containerFinder = find.byType(Container).first;
      expect(containerFinder, findsOneWidget);

      // Verify the widget has the correct dimensions
      final Container container = tester.widget(containerFinder);
      expect(container.constraints?.maxHeight, 90);
      expect(container.constraints?.maxWidth, 130);
    });

    testWidgets('InverterWidget displays inactive state correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: InverterWidget(inverterId: "test"),
            ),
          ),
        ),
      );

      // Since device_status defaults to 0 (inactive), verify inactive styling
      final Container statusIndicator = tester.widget(
        find.byType(Container).last, // Status indicator is the last container
      );

      // The status indicator should be red for inactive state
      expect(
        (statusIndicator.decoration as BoxDecoration).color,
        Colors.red,
      );
    });
  });
}
