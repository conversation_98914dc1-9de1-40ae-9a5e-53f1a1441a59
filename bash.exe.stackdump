Stack trace:
Frame         Function      Args
0007FFFF7F40  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF6E40) msys-2.0.dll+0x2118E
0007FFFF7F40  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF8218) msys-2.0.dll+0x69BA
0007FFFF7F40  0002100469F2 (00021028DF99, 0007FFFF7DF8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF7F40  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF7F40  00021006A545 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF8220  00021006B9A5 (0007FFFF7F50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD3B440000 ntdll.dll
7FFD39FD0000 KERNEL32.DLL
7FFD389E0000 KERNELBASE.dll
7FFD3A6A0000 USER32.dll
7FFD389B0000 win32u.dll
7FFD3B3D0000 GDI32.dll
000210040000 msys-2.0.dll
7FFD38680000 gdi32full.dll
7FFD385D0000 msvcp_win.dll
7FFD387C0000 ucrtbase.dll
7FFD3A0A0000 advapi32.dll
7FFD3A900000 msvcrt.dll
7FFD3AE90000 sechost.dll
7FFD3AC30000 RPCRT4.dll
7FFD37BC0000 CRYPTBASE.DLL
7FFD38910000 bcryptPrimitives.dll
7FFD39F90000 IMM32.DLL
