


import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:si/division1/model/solar_chart_model.dart';

import '../../services/firestore_services.dart';

abstract class ISolarChartRepository {
  Future<List<SolarChartFirestoreModel?>> getSolarChartData({required String siteId,DateTime? date});
  Future<int> getLength({required String siteId,DateTime dateTime});
}


class SolarChartRepository implements ISolarChartRepository{

  SolarChartRepository();

  final dbInstance = FirestoreService.instance;


  @override
  Future<int> getLength({ required String siteId,DateTime? dateTime}) async  {
    final day = dateTime ?? DateTime.now();
    final  collectionRef = FirebaseFirestore.instance.collection(siteId).where('time', isGreaterThan: DateTime(day.year, day.month, day.day-1,23,59,59))
        .where('timestamp', isLessThan: DateTime(day.year, day.month, day.day+1,0,0,0));
    final  snapshot = await collectionRef.count().get();
    return snapshot.count ?? 0;
  }

  @override
  Future<List<SolarChartFirestoreModel?>> getSolarChartData({required String siteId,DateTime? date}) async {
    final finalDate = date ?? DateTime.now();
    final beforeDay = DateTime(finalDate.year, finalDate.month, finalDate.day-1,23,59,59);
    final afterDay = DateTime(finalDate.year, finalDate.month, finalDate.day+1,0,0,0);
    try{
      final response =  dbInstance.collectionFuture(
          path: siteId,
          queryBuilder: (query) {
            return query
                .orderBy('time', descending: false)
                .where('time', isGreaterThan: beforeDay)
                .where('time', isLessThan: afterDay);
          },
          builder: (data, docId) {
            return SolarChartFirestoreModel.fromJson(data..addAll({'id': docId}));
          });
      return response;
    }catch(e){
      return [];
    }

  }



}