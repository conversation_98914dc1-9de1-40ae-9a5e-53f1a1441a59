
import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:si/division1/homepage/widgets/meter/sensor_widget.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/division1/model/motor_setting_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';
import 'package:si/utils/constants.dart';
import 'package:wave/config.dart';
import 'package:wave/wave.dart';

import '../../constants/app_sizes.dart';
import '../../services/shared_preferences_service.dart';
import '../model/tank_model.dart';
import '../provider/division_provider.dart';
import 'distribution_water_volume.dart';

class NewTankUnit extends HookConsumerWidget{

  static const _backgroundColor = Color(0xFFBAE5F3);

  NewTankUnit(this.roleType, this.tankId,this.tankSettingModel, this.height, this.width, {this.sensorId ="0",
  this.hasChlorine = false, this.hasTurbidity =false, this.hasPh = false, this.setting} );

  final int roleType;

  final String tankId;

  final String sensorId;

  final bool hasTurbidity;
  final bool hasChlorine;
  final bool hasPh;

  final double height;

  final double width;

  final TankSettingModel tankSettingModel;

  final DivisionSettingModel? setting;

  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final tankLevel = useState<int>(preference.getTankLevel(tankId));
    final sensorState = useState<SensorModel>(SensorModel());

      if(sensorId!="0"){
        ref.listen<AsyncValue<DatabaseEvent>>(sensorProvider(sensorId), (previous, next)  async {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            sensorState.value = SensorModel(
                tur: datasnapshot['tur'],
                cl: sensorId == "11" ? datasnapshot['cl'] : 0.01,
                ph: sensorId =="11" ? datasnapshot['ph'] : 7.1,
                time: datasnapshot['time'],
              );
          }
        });

      }


    ref.listen<AsyncValue<DatabaseEvent>>(tankProvider(tankId), (previous, next)  async {
      EasyLoading.dismiss();
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final aftertankModel = TankModel.fromJson(aftercleanData);
        final beforeData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final beforecleanData = Map<String, dynamic>.from(beforeData);
        final beforetankModel = TankModel.fromJson(beforecleanData);

        if(aftertankModel.level<= tankSettingModel.actual_height && aftertankModel.level!=0){
          await preference.setTankLevel(tankId:tankId,level: aftertankModel.level);
          tankLevel.value = aftertankModel.level;
          print("After Tank Level: ${aftertankModel.level}");
        }else{
          if(beforetankModel.level<=tankSettingModel.actual_height && aftertankModel.level!=0){
            await preference.setTankLevel(tankId:tankId,level: beforetankModel.level);
            tankLevel.value = beforetankModel.level;
          }
        }
      }
    });


    int difference = tankSettingModel.is_bw ? tankLevel.value.abs()  :(tankSettingModel.actual_height - tankLevel.value).abs();
    double perc = (difference + tankSettingModel.offset) / tankSettingModel.actual_height ;
    var percentage = (perc * 100).ceil();
    double correctedPerc = perc +0.1;
    return GestureDetector(
      onLongPress: () async {
        if(preference.getUserRole()>3){
          final result = await context.push("/home/<USER>", extra: tankId);
          if(result!=null && result == 'refresh'){
            ref.read(refreshProvider.notifier).state  = random(0, 9999999);
          }
        }
      },
      child: Container(
        width: width,
        decoration: BoxDecoration(
          border: Border.all(width: 1, color: Colors.black),
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8.0, left: 4, right: 4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                height: 35,
                child: Center(
                  child: Text(
                    preference.getUserRole()>2 ? tankSettingModel.name+"("+tankId+")" : tankSettingModel.name,
                    style: TextStyle(fontSize: 12),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              Stack(
                children: [
                  Center(
                    child: Container(
                      height: height,
                      width: width,
                      child: Stack(
                        children: [
                          GestureDetector(
                            child: WaveWidget(
                              config: CustomConfig(
                                colors: _colors,
                                durations: _durations,
                                heightPercentages: [1 - correctedPerc, 1 - correctedPerc],
                              ),
                              backgroundColor: _backgroundColor,
                              size: Size(double.infinity, double.infinity),
                              waveAmplitude: 0,
                              heightPercentage: 0.1,
                            ),
                          ),
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    '$percentage %',
                                    style: const TextStyle(fontSize: 20, ),
                                  ),
                                  if(sensorId!="0" && hasTurbidity)
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 10, // Adjust size as needed
                                        height: 10,
                                        decoration:  BoxDecoration(
                                          color: getTurbidityColor(double.parse((sensorState.value.tur ??  0.0).plus(getSensorSettingmodel(tankId, setting)?.tos ?? 0.0)).offsetTur(preference.getUserRole())),
                                          /*(sensorState.value.tur ?? 0.0) < 5.0 ? Colors.green :
                                          (sensorState.value.tur ?? 0.0) < 15.0 ? Colors.yellow : Colors.red, */// Change to any color you want
                                          shape: BoxShape.circle, /// Makes the container circular
                                        ),
                                      ),
                                      gapW8,
                                      Text(
                                        "Tur: ${double.parse((sensorState.value.tur ??  0.0).plus(getSensorSettingmodel(tankId, setting)?.tos ?? 0.0)).offsetTur(preference.getUserRole()).toStringAsFixed(2)} NTU" ,
                                        style: const TextStyle(fontSize: 8, ),
                                      ),
                                    ],
                                  ),
                                  gapH8,
                                  if(sensorId!="0" && hasPh)
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 10, // Adjust size as needed
                                        height: 10,
                                        decoration:  BoxDecoration(
                                          color: getPh(double.parse((sensorState.value.ph ??  0.0).plus(getSensorSettingmodel(tankId, setting)?.phos ?? 0.0)).offsetPh(preference.getUserRole())),
                                         /* ((sensorState.value.ph ?? 0.0) > 6.0  && (sensorState.value.ph ?? 0.0) < 8.0)? Colors.green :
                                          ((sensorState.value.ph ?? 0.0) > 5 &&   (sensorState.value.ph ?? 0.0) < 9.0)? Colors.yellow : Colors.red,*/ // Change to any color you want
                                          shape: BoxShape.circle, // Makes the container circular
                                        ),
                                      ),
                                      gapW8,
                                      Text(
                                        "Ph: ${double.parse((sensorState.value.ph ??  0.0).plus(getSensorSettingmodel(tankId, setting)?.phos ?? 0.0)).offsetPh(preference.getUserRole()).toStringAsFixed(2)}" ,
                                        style: const TextStyle(fontSize: 8, ),
                                      ),
                                    ],
                                  ),
                                  gapH8,
                                  if(sensorId!="0" && hasChlorine)
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 10, // Adjust size as needed
                                        height: 10,
                                        decoration:  BoxDecoration(
                                          color: getChlorine(double.parse((sensorState.value.cl ??  0.0).scale(getSensorSettingmodel(tankId, setting)?.clscale ?? 1).plus(getSensorSettingmodel(tankId, setting)?.clos ?? 0.0)).offsetCl(preference.getUserRole())),
                                        /*  (sensorState.value.cl ?? 0.0) < 0.5 ? Colors.green :
                                          (sensorState.value.cl ?? 0.0) < 1.0 ? Colors.yellow : Colors.red,*/ // Change to any color you want
                                          shape: BoxShape.circle, // Makes the container circular
                                        ),
                                      ),
                                      gapW8,
                                      Text("Cl: ${double.parse((sensorState.value.cl ??  0.0).scale(getSensorSettingmodel(tankId, setting)?.clscale ?? 1).plus(getSensorSettingmodel(tankId, setting)?.clos ?? 0.0)).offsetCl(preference.getUserRole()).toStringAsFixed(2)} NTU",
                                          style: TextStyle(color: Colors.black, fontSize: 8)),

                                    ],
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

}

class TankBlockWidget extends HookConsumerWidget {
  static const _backgroundColor = Color(0xFFBAE5F3);

  const TankBlockWidget(this.roleType, this.tankId,this.tankSettingModel, this.height, this.width );

  final int roleType;

  final String tankId;

  final double height;

  final double width;

  final TankSettingModel tankSettingModel;

  static const _colors = [
    Color(0xFF76BAE7),
    Colors.blueAccent,
  ];

  static const _durations = [
    5000,
    4000,
  ];

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final initialLevelProvider = ref.watch(futureTankProvider(tankId));
    final preference = ref.watch(sharedPreferencesServiceProvider);
    final tankLevel = useState<int>(preference.getTankLevel(tankId));


    ref.listen<AsyncValue<DatabaseEvent>>(tankProvider(tankId), (previous, next)  async {
      EasyLoading.dismiss();
      if (next.asData?.value.snapshot.value != null) {
        final afterData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final aftercleanData = Map<String, dynamic>.from(afterData);
        final aftertankModel = TankModel.fromJson(aftercleanData);
        final beforeData = next.asData?.value.snapshot.value as Map<Object?, Object?>;
        final beforecleanData = Map<String, dynamic>.from(beforeData);
        final beforetankModel = TankModel.fromJson(beforecleanData);
        if(aftertankModel.level<= aftertankModel.actual_height && aftertankModel.level!=0){
          await preference.setTankLevel(tankId:tankId,level: aftertankModel.level);
          tankLevel.value = aftertankModel.level;
        }else{
          if(beforetankModel.level<=aftertankModel.actual_height && aftertankModel.level!=0){
            await preference.setTankLevel(tankId:tankId,level: beforetankModel.level);
            tankLevel.value = beforetankModel.level;
          }
        }
      }

    });
    return initialLevelProvider.when(
        data: (_data){
          int difference = tankSettingModel.is_bw ? tankLevel.value.abs() :(tankSettingModel.actual_height - tankLevel.value).abs();
          double perc = difference / tankSettingModel.actual_height ;
          var percentage = (perc * 100).ceil();
          double correctedPerc = perc +0.1;
          return Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                tankSettingModel.name,
                style: TextStyle(fontSize: 14),
              ),

              Stack(
                children: [
                  Center(
                    child: Container(
                      height: height,
                      width: width,
                      child: Stack(
                        children: [
                          GestureDetector(
                            child: WaveWidget(
                              config: CustomConfig(
                                colors: _colors,
                                durations: _durations,
                                heightPercentages: [1 - correctedPerc, 1 - correctedPerc],
                              ),
                              backgroundColor: _backgroundColor,
                              size: Size(double.infinity, double.infinity),
                              waveAmplitude: 0,
                              heightPercentage: 0.1,
                            ),
                          ),
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                percentage.toString() + ' %',
                                style: TextStyle(fontSize: 20),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                ],
              ),


            ],
          );
        },
        error: (er,st){
          EasyLoading.dismiss();
          return Container();

        },
        loading: ()=>Container());


  }
}

SensorSettingModel? getSensorSettingmodel(String sensorId, DivisionSettingModel? setting){
  if(sensorId == "10"){
    return setting?.sensor10;
  }else if(sensorId == "11"){
    return setting?.sensor11;
  }else if(sensorId == "20"){
    return setting?.sensor20;
  }else if(sensorId == "21"){
    return setting?.sensor21;
  }else {
    return setting?.sensor32;
  }
}