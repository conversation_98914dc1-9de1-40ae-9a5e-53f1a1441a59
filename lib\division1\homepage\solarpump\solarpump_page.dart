

import 'dart:developer';

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/gogan/mobile_gogan_page.dart';
import 'package:si/division1/homepage/panbari/mobile_panbari_page.dart';
import 'package:si/division1/homepage/solarpump/mobile_solarpump_page.dart';

import '../../../common/widgets/base_scaffold.dart';
import '../../../common/widgets/widgets.dart';
import '../../../constants/app_sizes.dart';
import '../../../services/shared_preferences_service.dart';
import '../../../utils/app_colors.dart';
import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../new_motor_unit.dart';
import '../new_tank_unit.dart';
import '../widgets/inactive_valve_widget.dart';

class SolarpumpPage extends HookConsumerWidget{

  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));

    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });



    return siteSetting.when(
        success: (setting, message) {

          return BaseScaffold(
              showLeftIcon: false,
              appbarText: setting?.name ?? "",
              showAction: prefs.getUserRole() >3 ? true : false,
              onActionClick:() async {
                final setting = await context.push("/location");
              },
              child: DefaultTabController(
                length: 3, // Number of tabs
                child: Column(
                    children: [
                      Container(
                        color: AppColors.themeColor,
                        child: const TabBar(
                          tabs: [
                            Tab(text: 'System 1'),
                            Tab(text: 'System 2'),
                            Tab(text: 'System 2'),
                          ],
                          dividerColor: Colors.transparent,
                          indicatorSize: TabBarIndicatorSize.tab,
                          indicatorColor: Colors.white,
                          labelColor: AppColors.onThemeColor, // Customizing the TabBar
                          unselectedLabelColor: Colors.black54,
                          labelStyle: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                          unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal, fontSize: 14),
                        ),
                      ),
                      Expanded(
                        child: TabBarView(
                          children: [
                            MobileSolarPumpPage(setting!, "10"), // Corresponds to Tab 1
                            MobileSolarPumpPage(setting , "11"),
                            MobileSolarPumpPage(setting, "12")// Corresponds to Tab 2
                          ],
                        ),
                      ),
                    ],
                  ),

              ));




        },
        unInitialized: () {
          return Container();
        },
        error: (er) {
          log(er.toString());
          return Container();
        },
        unauthorized: () {
          return Container();
        },
        loading: () => LoadingIndicator());
  }


}