import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/homepage/widgets/pipeline.dart';
import 'package:si/division1/model/division_setting_model.dart';

import '../../model/motor_model.dart';
import '../../provider/division_provider.dart';
import '../widgets/meter/inverter_widget.dart';

class MobileSolarPumpPage extends HookConsumerWidget {
  final DivisionSettingModel? setting;
  final String? motorId;

  const MobileSolarPumpPage(this.setting, this.motorId,{super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final width = MediaQuery.of(context).size.width;
    final motor10State = useState<MotorModel>(MotorModel());

    final isRunning = (motor10State.value.motorAmps  ?? 0.01)> (setting?.motor10?.current_threshold ?? 0.01);
    ref.listen<AsyncValue<DatabaseEvent>>(motorProvider(motorId ?? "10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            motor10State.value = MotorModel(
                motorAmps: datasnapshot['current'],
                output_status: datasnapshot['output_status'],
                voltage: datasnapshot['voltage'],
                time: datasnapshot['time'],
                freq: datasnapshot['freq'],
                VoltageFaultStatus: datasnapshot['voltage_fault'],
                device_status: datasnapshot['device_status'],
                mobile_status: datasnapshot['mobile_status']);
          }
          if (motor10State.value.device_status == motor10State.value.mobile_status) {
            EasyLoading.dismiss();
          }
        });

    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          Expanded(
            flex: 4,
            child: Center(
              child: Container(
                width: 350,
                height: double.infinity,
                child: Stack(
                  children: [
                    Positioned(
                      bottom: 80+90+60+50,
                      left: 0,
                        child: Image.asset("assets/icons/sun.webp")),
                    Positioned(
                      bottom: 80+90+60,
                      left: width/2 - 140,
                      child: Image.asset("assets/icons/solar.webp"),
                    ),
                    Positioned(
                      left: width/2-23+90,
                      bottom: 100,
                      child: Image.asset("assets/icons/tanki.webp"),
                    ),
                    Positioned(
                      bottom: 80,
                      left: width/2 - 180,
                      child: InverterWidget(current: motor10State.value.motorAmps ?? 0.0,
                        voltage: motor10State.value.voltage ?? 0.0,
                        freq: motor10State.value.freq ?? 0.0,
                      currentThreshold: setting?.motor10?.current_threshold ?? 0.01,),
                    ),
                    Positioned(
                      bottom: 10,
                      left: width/2 + 20,
                      child: DischargeWidget(current: motor10State.value.motorAmps ?? 0.0,
                        voltage: motor10State.value.voltage ?? 0.0,
                        freq: motor10State.value.freq ?? 0.0,
                        currentThreshold: motorId == "10" ?
                        setting?.motor10?.current_threshold ?? 0.01 : motorId == "11"
                        ? setting?.motor11?.current_threshold ?? 0.01  : setting?.motor12?.current_threshold ?? 0.01,),
                    ),
                    Positioned(
                      bottom: 160-1,
                      left: width/2-23,
                      child: HorizontalPipeline(
                        leftFlow: false,
                        singleFlow: true,
                        isRight: false,
                        isFlowing: isRunning,
                        length: 91,

                      ),
                    ),
                    Positioned(
                      bottom: 0,
                      left: width/2-23,
                        child: VerticalPipeline(
                          topFlow: true,
                          length: 160,
                          isTop: false,
                          isFlowing: isRunning,
                          singleFlow: false,
                        ),

                    ),
                    Positioned(
                      bottom: 80+88,
                      left: width/2 - 90,
                      child: Container(
                        width: 3,
                        color: Colors.black54,
                        height: 75,
                      )
                    ),
                    Positioned(
                        bottom: 0,
                        left: width/2 - 110,
                        child: Container(
                          width: 3,
                          color: Colors.black54,
                          height: 80,
                        )
                    ),

                    Positioned(
                        bottom: 0,
                        left: width/2 - 110,
                        child: Container(
                          height: 3,
                          color: Colors.black54,
                          width: 86,
                        )
                    )


                  ],
                ),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              color: Colors.grey.shade400,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Image.asset(
                    "assets/icons/boring.webp",
                  ),
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      "Well",
                      style: TextStyle(fontSize: 14),
                    ),
                  )
                ],
              ),
            )
          )
        ],
      ),
    );
  }
}
