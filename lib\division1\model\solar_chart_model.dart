

import 'package:fl_chart/fl_chart.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../utils/timestamp_converter.dart';

part 'solar_chart_model.freezed.dart';
part 'solar_chart_model.g.dart';

@freezed
class SolarChartFirestoreModel with _$SolarChartFirestoreModel{
  const factory SolarChartFirestoreModel({
    int? amps,
    String? id,
    int? volt,
    int? siteId,
    int? motorId,
    int? freq,
    int? rssi,
    int? time,


  }) = _SolarChartFirestoreModel;

  factory SolarChartFirestoreModel.fromJson(Map<String, dynamic> json) => _$SolarChartFirestoreModelFromJson(json);
}


class SolarChartModel{
  SolarChartModel({
    this.ampList,
    this.voltageList,
    this.freqList,
    this.powerList,
    this.dischargeList,
    this.minX,
    this.minY,
    this.maxX,
    this.maxY
  }) ;

  final List<FlSpot>? ampList;
  final List<FlSpot>? voltageList;
  final List<FlSpot>? freqList;
  final List<FlSpot>? powerList;
  final List<FlSpot>? dischargeList;
  final double? minX;
  final double? minY;
  final double? maxX;
  final double? maxY;

}