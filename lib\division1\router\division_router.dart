import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/division1/history/logs_history_page.dart';
import 'package:si/division1/homepage/division_home_page.dart';
import 'package:si/division1/homepage/sindhupalchowk/sindhupalchowk_page.dart';
import 'package:si/division1/homepage/talkhu/talkhu_page.dart';
import 'package:si/division1/model/division_setting_model.dart';
import 'package:si/division1/passwordentry/password_entry_page.dart';
import 'package:si/division1/qr_scanner_page.dart';
import 'package:si/division1/settings/add_site_page.dart';
import 'package:si/division1/settings/bw_history_page.dart';
import 'package:si/division1/settings/delete_data_page.dart';
import 'package:si/division1/settings/division_settings_page.dart';
import 'package:si/division1/settings/flow_history_page.dart';
import 'package:si/division1/settings/install_date_page.dart';
import 'package:si/division1/settings/motor_setting_page.dart';
import 'package:si/division1/settings/new_division_history_page.dart';
import 'package:si/division1/settings/schedule_setting_page.dart';
import 'package:si/division1/settings/sensor_history_page.dart';
import 'package:si/division1/settings/tank_motor_setting_page.dart';
import 'package:si/division1/settings/valve_setting_page.dart';
import 'package:si/division1/userlist/user_list_page.dart';
import 'package:si/division1/valve/valve_page.dart';
import 'package:si/division1/homepage/inverter/inverter_chart_page.dart';
import 'package:si/provider/dashboard_provider.dart';
import 'package:si/services/shared_preferences_service.dart';

import '../../login/ui/login_page.dart';
import '../../main.dart';
import '../../provider/auth_provider.dart';
import '../../signup/ui/signup_page.dart';
import '../division_dashboard_page.dart';
import '../homepage/haripur/haripur_page.dart';
import '../homepage/sindhupalchowk/mobile_sindhulpalchowk_page.dart';
import '../homepage/talkhu/web/web_talkhu_page.dart';
import '../main_division.dart';
import '../passwordentry/site_list_page.dart';
import '../settings/division_history_page.dart';
import '../settings/tank_setting_page.dart';

enum AppRoute { login, signUp, forgetPassword, dashboard }

final ValueKey<String> _scaffoldKey = const ValueKey<String>('App scaffold');
final GlobalKey<NavigatorState> _rootNavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'root');
final GlobalKey<NavigatorState> _tabANavigatorKey =
    GlobalKey<NavigatorState>(debugLabel: 'tabANav');

final divisionRouter = Provider<GoRouter>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  final pref = ref.watch(sharedPreferencesServiceProvider);
  return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      redirect: (context, state) {
        final isLoggedIn = authRepository.isLogin();
        final width = MediaQuery.of(context).size.width;
        print(state.matchedLocation);

        if (isLoggedIn) {
          if (authRepository.hasSite()) {
            return state.matchedLocation;
          } else {
            print(state.matchedLocation);
            if (state.matchedLocation == '/scanner') {
              return '/scanner';
            } else if (state.matchedLocation == "/selectSite/addSite") {
              return '/selectSite/addSite';
            } else if (state.matchedLocation == '/selectSite') {
              return '/selectSite';
            } else {
              return '/password';
            }
          }
        } else {
          if (!isLoggedIn && state.matchedLocation == '/login') {
            return '/login';
          } else if (!isLoggedIn && state.matchedLocation == '/signup') {
            return '/signup';
          } else {
            return "/login";
          }
        }
      },
      routes: <RouteBase>[
        GoRoute(
          path: '/',
          redirect: (_, __) => '/home',
        ),
        GoRoute(
          path: '/login',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: LoginPage(),
          ),
        ),
        GoRoute(
          path: '/signup',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: const SignUpPage(),
          ),
        ),
        GoRoute(
          path: '/sindhulpalchowk',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: MobileSindhulpalchowkPage(),
          ),
        ),
        GoRoute(
          path: '/site4',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: SindhulpalchowkPage(),
          ),
        ),
        GoRoute(
          path: '/talkhu',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: TalkhuPage(),
          ),
        ),
        GoRoute(
          path: '/password',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: const PasswordEntryPage(),
          ),
        ),
        GoRoute(
          path: '/scanner',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              DivisionFadeTransitionPage(
            key: state.pageKey,
            child: const QRScannerPage(),
          ),
        ),
        GoRoute(
            path: '/selectSite',
            builder: (context, state) {
              final fromid = state.extra as int;
              return SiteListPage(fromPage: fromid);
            },
            routes: <RouteBase>[
              GoRoute(
                  path: 'addSite',
                  pageBuilder: (BuildContext context, GoRouterState state) {
                    return FadeTransitionPage(
                        key: state.pageKey, child: AddSitePage() // may be null
                        );
                  }),
            ]),
        GoRoute(
            path: '/location',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return FadeTransitionPage(
                  key: state.pageKey, child: InstallDatePage());
            }),
        GoRoute(
            path: '/history',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final siteId = state.extra as String;
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: NewDivisionHistoryPage(siteId: siteId) // may be null
                  );
            }),
        GoRoute(
            path: '/sensorHistory',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final siteId = state.extra as String;
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: SensorHistoryPage(siteId: siteId) // may be null
                  );
            }),
        GoRoute(
            path: '/bwHistory',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final siteId = state.extra as String;
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: BwHistoryPage(siteId: siteId) // may be null
                  );
            }),
        GoRoute(
            path: '/addSites',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return FadeTransitionPage(
                  key: state.pageKey, child: AddSitePage() // may be null
                  );
            }),
        GoRoute(
            path: '/logs',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final siteId = state.extra as String;
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: LogsHistoryPage(siteId: siteId) // may be null
                  );
            }),
        GoRoute(
            path: '/users',
            pageBuilder: (BuildContext context, GoRouterState state) {
              final siteId = state.extra as String;
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: UserListPage(siteId: siteId) // may be null
                  );
            }),
        GoRoute(
            path: '/deleteData',
            pageBuilder: (BuildContext context, GoRouterState state) {
              return FadeTransitionPage(
                  key: state.pageKey, child: const DeleteDataPage());
            }),
        /*  GoRoute(
            path: '/home',
            pageBuilder:
                (BuildContext context, GoRouterState state) {
              return FadeTransitionPage(
                  key: state.pageKey,
                  child: const DivisionHomePage() // may be null
              );
            }),*/
        StatefulShellRoute(
          builder: (context, state, navigationShell) {
            return navigationShell;
          },
          navigatorContainerBuilder: (context, navigationShell, children) {
            return DivisionDashboardPage(
                navigationShell: navigationShell, children: children);
          },
          branches: <StatefulShellBranch>[
            StatefulShellBranch(routes: <RouteBase>[
              GoRoute(
                  path: "/home",
                  builder: (context, state) {
                    return const DivisionHomePage();
                  },
                  routes: <RouteBase>[
                    GoRoute(
                        path: 'tankSetting',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final tankId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: TankSettingPage(tankId));
                        }),
                    GoRoute(
                        path: 'schedule',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final motorId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: MotorSettingPage(motorId));
                        }),
                    GoRoute(
                        path: 'motorSetting',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final motorId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: MotorSettingPage(motorId));
                        }),
                    GoRoute(
                        path: 'valveSetting',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final motorId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: ValveSettingPage(motorId));
                        }),
                    GoRoute(
                        path: 'inverterChart',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final inverterId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: InverterChartPage(inverterId: inverterId));
                        }),
                  ]),
            ]),
            /* if(pref.isValvePresent())
            StatefulShellBranch(
                routes: <RouteBase>[
                  GoRoute(
                      path: "/valve",
                      builder: (context, state) {
                        return const ValvePage();
                      },
                      routes: <RouteBase>[

                      ]),
                ]),*/
            StatefulShellBranch(routes: <RouteBase>[
              GoRoute(
                  path: "/settings",
                  builder: (context, state) {
                    return const DivisionSettingPage();
                  },
                  routes: <RouteBase>[
                    GoRoute(
                        path: 'addSite',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: AddSitePage() // may be null
                              );
                        }),
                    GoRoute(
                        path: 'flowHistory',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final siteId = "site1";
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child:
                                  FlowHistoryPage(siteId: siteId) // may be null
                              );
                        }),
                    GoRoute(
                        path: 'users',
                        pageBuilder:
                            (BuildContext context, GoRouterState state) {
                          final siteId = state.extra as String;
                          return FadeTransitionPage(
                              key: state.pageKey,
                              child: UserListPage(siteId: siteId) // may be null
                              );
                        }),
                  ]),
            ]),
          ],
        )
      ]);
});
