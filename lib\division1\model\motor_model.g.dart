// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'motor_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$MotorModelImpl _$$MotorModelImplFromJson(Map<String, dynamic> json) =>
    _$MotorModelImpl(
      motorAmps: (json['motorAmps'] as num?)?.toDouble() ?? 0.0,
      device_status: (json['device_status'] as num?)?.toInt() ?? 0,
      mobile_status: (json['mobile_status'] as num?)?.toInt() ?? 0,
      output_status: (json['output_status'] as num?)?.toInt() ?? 0,
      time: (json['time'] as num?)?.toInt() ?? 0,
      VoltageFaultStatus: (json['VoltageFaultStatus'] as num?)?.toInt() ?? 2,
      voltage: (json['voltage'] as num?)?.toDouble() ?? 0.0,
      freq: (json['freq'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$MotorModelImplToJson(_$MotorModelImpl instance) =>
    <String, dynamic>{
      'motorAmps': instance.motorAmps,
      'device_status': instance.device_status,
      'mobile_status': instance.mobile_status,
      'output_status': instance.output_status,
      'time': instance.time,
      'VoltageFaultStatus': instance.VoltageFaultStatus,
      'voltage': instance.voltage,
      'freq': instance.freq,
    };

_$SensorModelImpl _$$SensorModelImplFromJson(Map<String, dynamic> json) =>
    _$SensorModelImpl(
      cl: (json['cl'] as num?)?.toDouble() ?? 0.0,
      tur: (json['tur'] as num?)?.toDouble() ?? 0.0,
      ph: (json['ph'] as num?)?.toDouble() ?? 0.0,
      flow: (json['flow'] as num?)?.toDouble() ?? 0.0,
      cum_flow: (json['cum_flow'] as num?)?.toDouble() ?? 0.0,
      time: (json['time'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$SensorModelImplToJson(_$SensorModelImpl instance) =>
    <String, dynamic>{
      'cl': instance.cl,
      'tur': instance.tur,
      'ph': instance.ph,
      'flow': instance.flow,
      'cum_flow': instance.cum_flow,
      'time': instance.time,
    };
