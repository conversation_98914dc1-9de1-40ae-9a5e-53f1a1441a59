

import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/constants/app_sizes.dart';
import 'package:si/division1/homepage/new_tank_unit.dart';
import 'package:si/division1/homepage/widgets/distribution_widget.dart';
import 'package:si/division1/homepage/widgets/meter/flow_meter_widget.dart';
import 'package:si/division1/homepage/widgets/meter/sensor_widget.dart';
import 'package:si/division1/homepage/widgets/valve_widget.dart';
import 'package:si/division1/model/motor_model.dart';
import 'package:si/division1/model/tank_setting_model.dart';

import '../../../services/shared_preferences_service.dart';
import '../../provider/division_provider.dart';

class MobileSindhulpalchowkPage extends HookConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final prefs = ref.watch(sharedPreferencesServiceProvider);
    //final siteSetting = ref.watch(siteSettingProvider(prefs.getSiteId()));
    final siteSetting = ref.watch(divisionStateProvider(prefs.getSiteId()));
    ref.listen(refreshProvider, (previous, next)  async {
      ref.read(divisionStateProvider(prefs.getSiteId()).notifier).getSetting(prefs.getSiteId());
    });

    final valveState = useState<MotorModel>(MotorModel());

    ref.listen<AsyncValue<DatabaseEvent>>(valveProvider("10"),
            (previous, next) {
          if (next.asData?.value.snapshot.value != null) {
            final datasnapshot = next.asData?.value.snapshot.value as Map;
            valveState.value = MotorModel(
              device_status: datasnapshot['device_status'],
              mobile_status: datasnapshot['mobile_status'],
              output_status: datasnapshot['output_status']!=null ? datasnapshot["output_status"] : 0.0,
            );
          }
          if(valveState.value.mobile_status == valveState.value.device_status){
            EasyLoading.dismiss();
          }
        });
    return siteSetting.when(success: (data,message){
          return BaseScaffold(
            showLeftIcon: false,
              appbarText: "Sindhupalchowk",
              child: Center(
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFF87CEFA), // Light Blue (Sky color)
                        Color(0xFFE0FFFF),
                        Color(0xFF87CEFA), // Light Blue (Sky color)
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  width: double.infinity,
                  child: CustomScrollView(
                    slivers: [
                      SliverToBoxAdapter(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            gapH8,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                SingleSensorWidget(listenerRequired: true, isPortrait: true,
                                sensorModel: SensorModel(),
                                sensorId: "10",),
                                Stack(
                                  children: [
                                    Container(
                                      height: 120,
                                      width: 100,
                                    ),
                                    Image.asset(
                                      "assets/icons/open_well.png",
                                      height: 80,
                                      width: 100,
                                    ),
                                    Positioned(
                                      top: 70,
                                      left: 0,
                                      right: 0,
                                      child: Center(
                                        child: Container(
                                          width: 20,
                                          height: 50,
                                          color: Colors.lightBlue,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                            Container(
                              width: double.infinity,
                              height: 20,
                              color: Colors.lightBlue,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Expanded(
                                  child: Column(
                                    children: [
                                      Container(
                                        width: 20,
                                        height: 40,
                                        color: Colors.lightBlue,
                                      ),
                                      ValveWidget(name: data?.valve11?.name ?? 'Valve 11',
                                          valveId: "11",
                                          active: false, role: 3,
                                          ratio: 2.5, valveModel: valveState.value),
                                      Container(
                                        width: 20,
                                        height: 65,
                                        color: valveState.value.device_status==3 ? Colors.grey : Colors.lightBlue,
                                      ),
                                     MobileFlowMeterWidget(
                                       sensorId: "10",
                                     ),
                                      Container(
                                        width: 20,
                                        height: 61,
                                        color: valveState.value.device_status==3 ? Colors.grey : Colors.lightBlue,
                                      ),

                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 20,
                                        height: 40,
                                        color: Colors.lightBlue,
                                      ),
                                      ValveWidget(name: data?.valve10?.name ?? 'Valve 10',
                                          valveId: "10",
                                          active: true, role: 3,
                                          ratio: 2.5, valveModel: valveState.value),
                                      Center(
                                        child: UpdownDistributionWidget(name: "Wash out",
                                            isFlowing: valveState.value.device_status == 3 ? true : false,
                                            height: 80, width: 40,turn: 0),
                                      ),
                                      gapH90

                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Image.asset("assets/icons/sediment.png",
                                  width: double.infinity),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Container(
                                  width: 20,
                                  height: 120,
                                  color: Colors.lightBlue,
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                                  child: SingleSensorWidget(isPortrait: true,
                                  listenerRequired: true,
                                  sensorModel: SensorModel(),
                                  sensorId: "11",),
                                ),


                              ],
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16.0),
                              child: Image.asset("assets/icons/roughing_filter.png",
                                  width: double.infinity, height: 150, fit: BoxFit.fill),
                            ),

                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Container(
                                  width: 20,
                                  height: 100,
                                  color: Colors.lightBlue,
                                ),
                                Column(
                                  children: [
                                    gapH8,
                                    ThreeSensorWidget(
                                      isPortrait: true,sensorId: "12",),
                                    gapH8,

                                  ],
                                ),
                                Container(
                                  width: 20,
                                  height: 100,
                                  color: Colors.lightBlue,
                                ),
                              ],
                            ),
                            Container(
                              width: double.infinity,
                              height: 20,
                              color: Colors.lightBlue,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Column(
                                  children: [
                                    Container(
                                      height: 30,
                                      width: 20,
                                      color: Colors.lightBlue,
                                    ),
                                    NewTankUnit(prefs.getUserRole(), "10", data!.tank10!, 120, 100)
                                  ],
                                ),
                                Column(
                                  children: [
                                    Container(
                                      height: 30,
                                      width: 20,
                                      color: Colors.lightBlue,
                                    ),
                                    NewTankUnit(prefs.getUserRole(), "11", data!.tank11!, 120, 100)
                                  ],
                                )
                              ],
                            ),
                            gapH16





                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ));
        },
        unInitialized: ()=>Container(),
        error: (er){
          return Container();
        },
        unauthorized: ()=>Container(),
        loading: ()=>LoadingIndicator());


  }

}