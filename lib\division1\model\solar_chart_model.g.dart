// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'solar_chart_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SolarChartFirestoreModelImpl _$$SolarChartFirestoreModelImplFromJson(
        Map<String, dynamic> json) =>
    _$SolarChartFirestoreModelImpl(
      amps: (json['amps'] as num?)?.toInt(),
      id: json['id'] as String?,
      volt: (json['volt'] as num?)?.toInt(),
      siteId: (json['siteId'] as num?)?.toInt(),
      motorId: (json['motorId'] as num?)?.toInt(),
      freq: (json['freq'] as num?)?.toInt(),
      rssi: (json['rssi'] as num?)?.toInt(),
      time: (json['time'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$SolarChartFirestoreModelImplToJson(
        _$SolarChartFirestoreModelImpl instance) =>
    <String, dynamic>{
      'amps': instance.amps,
      'id': instance.id,
      'volt': instance.volt,
      'siteId': instance.siteId,
      'motorId': instance.motorId,
      'freq': instance.freq,
      'rssi': instance.rssi,
      'time': instance.time,
    };
