import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:si/common/widgets/base_scaffold.dart';
import 'package:si/common/widgets/custom_loading_indicator.dart';
import 'package:si/division1/homepage/inverter/inverter_chart_controller.dart';
import 'package:si/division1/homepage/inverter/inverter_current_chart.dart';
import 'package:si/division1/homepage/inverter/inverter_frequency_chart.dart';
import 'package:si/division1/homepage/inverter/inverter_power_chart.dart';
import 'package:si/kawosoti/schema/widgets/date_forward_backward.dart';

import '../../../constants/app_sizes.dart';

class InverterChartPage extends HookConsumerWidget {
  final String inverterId;

  const InverterChartPage({required this.inverterId, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final chartState = ref.watch(inverterChartControllerProvider(inverterId));
    final chartController =
        ref.read(inverterChartControllerProvider(inverterId).notifier);

    return BaseScaffold(
      appbarText: 'Inverter Charts - $inverterId',
      showLeftIcon: true,
      child: chartState.when(
        unInitialized: () {
          // Load data for today when first initialized
          WidgetsBinding.instance.addPostFrameCallback((_) {
            chartController.getInverterChartData(dateTime: DateTime.now());
          });
          return const LoadingIndicator();
        },
        loading: () => const LoadingIndicator(),
        error: (error) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red),
              gapH16,
              Text(
                'Error loading data',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              gapH8,
              Text(
                error,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              gapH16,
              ElevatedButton(
                onPressed: () {
                  chartController.getInverterChartData(
                      dateTime: DateTime.now());
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
        success: (chartModel, message) => SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date selector
              DateForwardBackward(
                onDateChange: (selectedDate) {
                  chartController.getInverterChartData(dateTime: selectedDate);
                },
              ),
              gapH24,

              // Current Chart
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current (A)',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      gapH16,
                      SizedBox(
                        height: 300,
                        child: InverterCurrentChart(chartModel: chartModel),
                      ),
                    ],
                  ),
                ),
              ),
              gapH24,

              // Power Chart
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Power (W)',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      gapH16,
                      SizedBox(
                        height: 300,
                        child: InverterPowerChart(chartModel: chartModel),
                      ),
                    ],
                  ),
                ),
              ),
              gapH24,

              // Frequency Chart
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Frequency (Hz)',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      gapH16,
                      SizedBox(
                        height: 300,
                        child: InverterFrequencyChart(chartModel: chartModel),
                      ),
                    ],
                  ),
                ),
              ),
              gapH24,
            ],
          ),
        ),
      ),
    );
  }
}
